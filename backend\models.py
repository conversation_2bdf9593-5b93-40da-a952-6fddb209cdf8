"""
Database models for the Time and Attendance System
"""

from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash
import secrets
import string

# Import db from a shared database module to avoid circular imports
from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

class User(db.Model):
    """User model for admin/manager authentication"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    role = db.Column(db.Enum('admin', 'manager', 'viewer'), nullable=False, default='viewer')
    last_login = db.Column(db.DateTime)
    status = db.Column(db.Enum('active', 'inactive'), nullable=False, default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    approved_leave_requests = db.relationship('LeaveRequest', foreign_keys='LeaveRequest.approver_id', backref='approver')
    system_logs = db.relationship('SystemLog', backref='user')

    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'role': self.role,
            'status': self.status,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Employee(db.Model):
    """Employee model for attendance tracking"""
    __tablename__ = 'employees'

    id = db.Column(db.Integer, primary_key=True)
    rfid_tag = db.Column(db.String(50), unique=True, nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(100), unique=True)
    phone = db.Column(db.String(20))
    department = db.Column(db.String(50))
    position = db.Column(db.String(50))
    hourly_rate = db.Column(db.Numeric(10, 2), nullable=False)
    leave_balance = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    sick_leave_balance = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    employment_start_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.Enum('active', 'inactive'), nullable=False, default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    attendance_records = db.relationship('AttendanceRecord', backref='employee', cascade='all, delete-orphan')
    leave_requests = db.relationship('LeaveRequest', backref='employee', cascade='all, delete-orphan')

    @property
    def full_name(self):
        """Get full name"""
        return f"{self.first_name} {self.last_name}"

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'rfid_tag': self.rfid_tag,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'email': self.email,
            'phone': self.phone,
            'department': self.department,
            'position': self.position,
            'hourly_rate': float(self.hourly_rate),
            'leave_balance': float(self.leave_balance),
            'sick_leave_balance': float(self.sick_leave_balance),
            'employment_start_date': self.employment_start_date.isoformat(),
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class AttendanceRecord(db.Model):
    """Attendance record model"""
    __tablename__ = 'attendance_records'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False)
    type = db.Column(db.Enum('clock_in', 'clock_out'), nullable=False)
    device_id = db.Column(db.String(50), nullable=False)
    status = db.Column(db.Enum('regular', 'auto_checkout', 'manual_adjustment'), nullable=False, default='regular')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'employee_name': self.employee.full_name if self.employee else None,
            'timestamp': self.timestamp.isoformat(),
            'type': self.type,
            'device_id': self.device_id,
            'status': self.status,
            'notes': self.notes,
            'created_at': self.created_at.isoformat()
        }

class LeaveRequest(db.Model):
    """Leave request model"""
    __tablename__ = 'leave_requests'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    type = db.Column(db.Enum('sick_leave', 'regular_leave'), nullable=False)
    status = db.Column(db.Enum('pending', 'approved', 'rejected'), nullable=False, default='pending')
    approver_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    request_date = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'employee_name': self.employee.full_name if self.employee else None,
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'type': self.type,
            'status': self.status,
            'approver_id': self.approver_id,
            'approver_name': self.approver.full_name if self.approver else None,
            'request_date': self.request_date.isoformat(),
            'notes': self.notes
        }

class Device(db.Model):
    """Device model for ESP32 devices"""
    __tablename__ = 'devices'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    location = db.Column(db.String(100))
    mac_address = db.Column(db.String(17), unique=True, nullable=False)
    ip_address = db.Column(db.String(45))
    api_key = db.Column(db.String(255), unique=True, nullable=False)
    last_sync = db.Column(db.DateTime)
    status = db.Column(db.Enum('online', 'offline'), default='offline')
    firmware_version = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, **kwargs):
        super(Device, self).__init__(**kwargs)
        if not self.api_key:
            self.api_key = self.generate_api_key()

    @staticmethod
    def generate_api_key():
        """Generate a secure API key"""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(64))

    def regenerate_api_key(self):
        """Regenerate API key"""
        self.api_key = self.generate_api_key()
        return self.api_key

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'name': self.name,
            'location': self.location,
            'mac_address': self.mac_address,
            'ip_address': self.ip_address,
            'api_key': self.api_key,
            'last_sync': self.last_sync.isoformat() if self.last_sync else None,
            'status': self.status,
            'firmware_version': self.firmware_version,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class SystemLog(db.Model):
    """System log model for audit trail"""
    __tablename__ = 'system_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    action = db.Column(db.String(100), nullable=False)
    entity_type = db.Column(db.String(50))
    entity_id = db.Column(db.Integer)
    details = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'user_name': self.user.username if self.user else None,
            'action': self.action,
            'entity_type': self.entity_type,
            'entity_id': self.entity_id,
            'details': self.details,
            'ip_address': self.ip_address,
            'timestamp': self.timestamp.isoformat()
        }

class Setting(db.Model):
    """System settings model"""
    __tablename__ = 'settings'

    id = db.Column(db.Integer, primary_key=True)
    setting_key = db.Column(db.String(50), unique=True, nullable=False)
    setting_value = db.Column(db.Text, nullable=False)
    description = db.Column(db.Text)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'key': self.setting_key,
            'value': self.setting_value,
            'description': self.description,
            'updated_at': self.updated_at.isoformat()
        }
