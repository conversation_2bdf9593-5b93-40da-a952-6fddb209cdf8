"""
User management routes for the Time and Attendance System
"""

from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import or_

from models import User, db
from utils.validators import validate_user_data, validate_pagination_params
from utils.logging import log_user_action
from utils.auth import require_role

users_bp = Blueprint('users', __name__)

@users_bp.route('', methods=['GET'])
@jwt_required()
@require_role(['admin'])
def get_users():
    """
    Get all users (admin only)
    """
    try:
        # Get query parameters
        role = request.args.get('role')
        status = request.args.get('status')
        search = request.args.get('search')
        page = request.args.get('page', '1')
        limit = request.args.get('limit', '20')

        # Validate pagination
        page_num, limit_num = validate_pagination_params(page, limit)

        # Build query
        query = User.query

        # Apply filters
        if role:
            if role not in ['admin', 'manager', 'viewer']:
                return jsonify({'error': 'Invalid role'}), 400
            query = query.filter(User.role == role)

        if status:
            if status not in ['active', 'inactive']:
                return jsonify({'error': 'Invalid status'}), 400
            query = query.filter(User.status == status)

        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    User.username.ilike(search_term),
                    User.email.ilike(search_term),
                    User.first_name.ilike(search_term),
                    User.last_name.ilike(search_term)
                )
            )

        # Order by username
        query = query.order_by(User.username)

        # Paginate
        pagination = query.paginate(
            page=page_num,
            per_page=limit_num,
            error_out=False
        )

        users = [user.to_dict() for user in pagination.items]

        return jsonify({
            'data': users,
            'total': pagination.total,
            'page': page_num,
            'limit': limit_num,
            'pages': pagination.pages
        }), 200

    except Exception as e:
        current_app.logger.error(f'Get users error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@users_bp.route('', methods=['POST'])
@jwt_required()
@require_role(['admin'])
def create_user():
    """
    Create new user (admin only)
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate data
        validation_error = validate_user_data(data)
        if validation_error:
            return jsonify({'error': validation_error}), 400

        # Check if username already exists
        existing_user = User.query.filter_by(username=data['username']).first()
        if existing_user:
            return jsonify({'error': 'Username already exists'}), 409

        # Check if email already exists
        existing_email = User.query.filter_by(email=data['email']).first()
        if existing_email:
            return jsonify({'error': 'Email already exists'}), 409

        # Create user
        user = User(
            username=data['username'],
            email=data['email'],
            first_name=data['first_name'],
            last_name=data['last_name'],
            role=data.get('role', 'viewer'),
            status=data.get('status', 'active')
        )

        # Set password
        user.set_password(data['password'])

        db.session.add(user)
        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='create',
            entity_type='user',
            entity_id=user.id,
            details=f'Created user: {user.username}'
        )

        return jsonify(user.to_dict()), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Create user error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@users_bp.route('/<int:user_id>', methods=['PUT'])
@jwt_required()
@require_role(['admin'])
def update_user(user_id):
    """
    Update user (admin only)
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate data
        validation_error = validate_user_data(data, is_update=True)
        if validation_error:
            return jsonify({'error': validation_error}), 400

        # Check if email already exists (if changed)
        if 'email' in data and data['email'] != user.email:
            existing_email = User.query.filter_by(email=data['email']).first()
            if existing_email:
                return jsonify({'error': 'Email already exists'}), 409

        # Update fields
        if 'email' in data:
            user.email = data['email']
        if 'first_name' in data:
            user.first_name = data['first_name']
        if 'last_name' in data:
            user.last_name = data['last_name']
        if 'role' in data:
            user.role = data['role']
        if 'status' in data:
            user.status = data['status']

        user.updated_at = datetime.utcnow()
        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='update',
            entity_type='user',
            entity_id=user.id,
            details=f'Updated user: {user.username}'
        )

        return jsonify(user.to_dict()), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Update user error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@users_bp.route('/<int:user_id>/password', methods=['PUT'])
@jwt_required()
def update_user_password(user_id):
    """
    Update user password (admin or self)
    """
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)

        if not current_user:
            return jsonify({'error': 'Current user not found'}), 401

        # Check if user can update this password
        if current_user.role != 'admin' and current_user_id != user_id:
            return jsonify({'error': 'Can only update your own password'}), 403

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        password = data.get('password')
        if not password:
            return jsonify({'error': 'Password is required'}), 400

        if len(password) < 8:
            return jsonify({'error': 'Password must be at least 8 characters long'}), 400

        # Set new password
        user.set_password(password)
        user.updated_at = datetime.utcnow()
        db.session.commit()

        # Log action
        log_user_action(
            user_id=current_user_id,
            action='password_update',
            entity_type='user',
            entity_id=user.id,
            details=f'Updated password for user: {user.username}'
        )

        return jsonify({'message': 'Password updated successfully'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Update user password error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@users_bp.route('/<int:user_id>', methods=['DELETE'])
@jwt_required()
@require_role(['admin'])
def delete_user(user_id):
    """
    Delete user (admin only)
    """
    try:
        current_user_id = get_jwt_identity()

        # Prevent self-deletion
        if current_user_id == user_id:
            return jsonify({'error': 'Cannot delete your own account'}), 400

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        username = user.username

        # Set to inactive instead of deleting to preserve audit trail
        user.status = 'inactive'
        user.updated_at = datetime.utcnow()
        db.session.commit()

        # Log action
        log_user_action(
            user_id=current_user_id,
            action='delete',
            entity_type='user',
            entity_id=user.id,
            details=f'Deactivated user: {username}'
        )

        return jsonify({'message': 'User deactivated successfully'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Delete user error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500
