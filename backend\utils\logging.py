"""
Logging utilities for the Time and Attendance System
"""

from flask import request
from models import SystemLog, db

def log_user_action(user_id: int, action: str, entity_type: str = None,
                   entity_id: int = None, details: str = None,
                   ip_address: str = None):
    """
    Log user action to the system logs

    Args:
        user_id: ID of the user performing the action
        action: Action being performed (e.g., 'create', 'update', 'delete', 'login')
        entity_type: Type of entity being acted upon (e.g., 'employee', 'attendance')
        entity_id: ID of the entity being acted upon
        details: Additional details about the action
        ip_address: IP address of the user
    """
    try:
        if not ip_address:
            ip_address = request.remote_addr if request else None

        log_entry = SystemLog(
            user_id=user_id,
            action=action,
            entity_type=entity_type,
            entity_id=entity_id,
            details=details,
            ip_address=ip_address
        )

        db.session.add(log_entry)
        db.session.commit()

    except Exception as e:
        # Don't let logging errors break the main functionality
        print(f"Logging error: {str(e)}")
        db.session.rollback()

def log_device_action(device_id: str, action: str, details: str = None):
    """
    Log device action (for ESP32 devices)

    Args:
        device_id: ID of the device performing the action
        action: Action being performed
        details: Additional details about the action
    """
    try:
        log_entry = SystemLog(
            user_id=None,  # Device actions don't have a user
            action=f"device_{action}",
            entity_type='device',
            entity_id=device_id,
            details=details,
            ip_address=request.remote_addr if request else None
        )

        db.session.add(log_entry)
        db.session.commit()

    except Exception as e:
        print(f"Device logging error: {str(e)}")
        db.session.rollback()

def log_system_event(action: str, details: str = None):
    """
    Log system-level events

    Args:
        action: System action being performed
        details: Additional details about the event
    """
    try:
        log_entry = SystemLog(
            user_id=None,
            action=f"system_{action}",
            entity_type='system',
            entity_id=None,
            details=details,
            ip_address=None
        )

        db.session.add(log_entry)
        db.session.commit()

    except Exception as e:
        print(f"System logging error: {str(e)}")
        db.session.rollback()
