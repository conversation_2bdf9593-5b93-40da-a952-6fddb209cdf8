import React, { useEffect, useState } from 'react';
import { Settings as SettingsIcon, Save, RotateCcw, Plus, Trash2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorMessage from '@/components/common/ErrorMessage';
import { apiService } from '@/services/api';
import { Setting } from '@/types/api';
import { getErrorMessage } from '@/lib/utils';
import { usePermissions } from '@/contexts/AuthContext';

interface SettingGroup {
  title: string;
  description: string;
  settings: Setting[];
}

const Settings: React.FC = () => {
  const [settings, setSettings] = useState<Setting[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [modifiedSettings, setModifiedSettings] = useState<Record<string, string>>({});

  const permissions = usePermissions();

  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.getSettings();
      setSettings(response.data);
    } catch (err) {
      setError(getErrorMessage(err));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const handleSettingChange = (key: string, value: string) => {
    setModifiedSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      setError(null);
      setSuccessMessage(null);

      // Save each modified setting
      for (const [key, value] of Object.entries(modifiedSettings)) {
        await apiService.updateSetting(key, { value });
      }

      setModifiedSettings({});
      setSuccessMessage('Settings saved successfully!');
      await fetchSettings();

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      setError(getErrorMessage(err));
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetSettings = () => {
    setModifiedSettings({});
    setError(null);
    setSuccessMessage(null);
  };

  const handleInitializeSettings = async () => {
    try {
      setIsSaving(true);
      setError(null);
      await apiService.initializeSettings();
      setSuccessMessage('Default settings initialized successfully!');
      await fetchSettings();
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      setError(getErrorMessage(err));
    } finally {
      setIsSaving(false);
    }
  };

  const getSettingValue = (key: string): string => {
    return modifiedSettings[key] ?? settings.find(s => s.key === key)?.value ?? '';
  };

  const groupSettings = (): SettingGroup[] => {
    const groups: SettingGroup[] = [
      {
        title: 'Company Information',
        description: 'Basic company details and branding',
        settings: settings.filter(s => ['company_name', 'timezone', 'currency'].includes(s.key))
      },
      {
        title: 'Work Schedule',
        description: 'Configure work hours and attendance policies',
        settings: settings.filter(s => ['work_hours_per_day', 'work_days_per_week', 'auto_checkout_time', 'overtime_multiplier'].includes(s.key))
      },
      {
        title: 'Leave Management',
        description: 'Leave policies and balances',
        settings: settings.filter(s => ['annual_leave_days', 'sick_leave_days'].includes(s.key))
      },
      {
        title: 'Notifications',
        description: 'Email and system notifications',
        settings: settings.filter(s => ['email_notifications'].includes(s.key))
      },
      {
        title: 'Other Settings',
        description: 'Additional system configurations',
        settings: settings.filter(s => !['company_name', 'timezone', 'currency', 'work_hours_per_day', 'work_days_per_week', 'auto_checkout_time', 'overtime_multiplier', 'annual_leave_days', 'sick_leave_days', 'email_notifications'].includes(s.key))
      }
    ].filter(group => group.settings.length > 0);

    return groups;
  };

  const renderSettingInput = (setting: Setting) => {
    const value = getSettingValue(setting.key);
    const isBoolean = value === 'true' || value === 'false';
    const isTime = setting.key.includes('time');
    const isNumber = ['work_hours_per_day', 'work_days_per_week', 'annual_leave_days', 'sick_leave_days', 'overtime_multiplier'].includes(setting.key);

    if (isBoolean) {
      return (
        <div className="flex items-center space-x-2">
          <Switch
            checked={value === 'true'}
            onCheckedChange={(checked) => handleSettingChange(setting.key, checked ? 'true' : 'false')}
          />
          <Label>{value === 'true' ? 'Enabled' : 'Disabled'}</Label>
        </div>
      );
    }

    if (isTime) {
      return (
        <Input
          type="time"
          value={value}
          onChange={(e) => handleSettingChange(setting.key, e.target.value)}
        />
      );
    }

    if (isNumber) {
      return (
        <Input
          type="number"
          value={value}
          onChange={(e) => handleSettingChange(setting.key, e.target.value)}
          min="0"
          step={setting.key === 'overtime_multiplier' ? '0.1' : '1'}
        />
      );
    }

    return (
      <Input
        value={value}
        onChange={(e) => handleSettingChange(setting.key, e.target.value)}
        placeholder={setting.description}
      />
    );
  };

  if (!permissions.canManageSettings()) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <SettingsIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
          <p className="mt-1 text-sm text-gray-500">
            You don't have permission to access system settings.
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
          <p className="text-muted-foreground">
            Configure system-wide settings and preferences
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleInitializeSettings}
            disabled={isSaving}
          >
            <Plus className="w-4 h-4 mr-2" />
            Initialize Defaults
          </Button>
          <Button
            variant="outline"
            onClick={handleResetSettings}
            disabled={Object.keys(modifiedSettings).length === 0}
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset Changes
          </Button>
          <Button
            onClick={handleSaveSettings}
            disabled={Object.keys(modifiedSettings).length === 0 || isSaving}
          >
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Messages */}
      {error && <ErrorMessage message={error} />}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="text-sm text-green-600">{successMessage}</div>
        </div>
      )}

      {/* Settings Groups */}
      <div className="space-y-6">
        {groupSettings().map((group, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle>{group.title}</CardTitle>
              <CardDescription>{group.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {group.settings.map((setting, settingIndex) => (
                <div key={setting.key}>
                  <div className="space-y-2">
                    <Label htmlFor={setting.key} className="text-sm font-medium">
                      {setting.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Label>
                    {renderSettingInput(setting)}
                    {setting.description && (
                      <p className="text-xs text-muted-foreground">
                        {setting.description}
                      </p>
                    )}
                  </div>
                  {settingIndex < group.settings.length - 1 && (
                    <Separator className="mt-6" />
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Modified Settings Indicator */}
      {Object.keys(modifiedSettings).length > 0 && (
        <div className="fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2">
            <SettingsIcon className="w-4 h-4" />
            <span className="text-sm">
              {Object.keys(modifiedSettings).length} setting(s) modified
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default Settings;
