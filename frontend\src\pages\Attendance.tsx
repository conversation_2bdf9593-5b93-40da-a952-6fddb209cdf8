import React, { useEffect, useState } from 'react';
import { Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorMessage from '@/components/common/ErrorMessage';
import RealTimeAttendanceUpdates from '@/components/attendance/RealTimeAttendanceUpdates';
import StatusBadge from '@/components/common/StatusBadge';
import Pagination from '@/components/common/Pagination';
import { apiService } from '@/services/api';
import { AttendanceRecord, AttendanceFilters, Employee } from '@/types/api';
import { formatDateTime, formatTime, getErrorMessage } from '@/lib/utils';

const Attendance: React.FC = () => {
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [filters, setFilters] = useState<AttendanceFilters>({
    page: 1,
    limit: 20,
  });

  const fetchAttendanceRecords = async (newFilters?: AttendanceFilters) => {
    try {
      setIsLoading(true);
      setError(null);

      const filtersToUse = newFilters || filters;
      const response = await apiService.getAttendance(filtersToUse);

      setAttendanceRecords(response.data);
      setTotalRecords(response.total);
      setTotalPages(response.pages);
      setCurrentPage(response.page);
    } catch (err) {
      setError(getErrorMessage(err));
    } finally {
      setIsLoading(false);
    }
  };

  const fetchEmployees = async () => {
    try {
      const response = await apiService.getEmployees({ limit: 1000 });
      setEmployees(response.data);
    } catch (err) {
      console.error('Failed to fetch employees:', err);
    }
  };

  useEffect(() => {
    fetchAttendanceRecords();
    fetchEmployees();
  }, []);

  const handleEmployeeFilter = (employeeId: string) => {
    const newFilters = {
      ...filters,
      employee_id: employeeId === 'all' ? undefined : parseInt(employeeId),
      page: 1
    };
    setFilters(newFilters);
    fetchAttendanceRecords(newFilters);
  };

  const handleTypeFilter = (type: string) => {
    const newFilters = {
      ...filters,
      type: type === 'all' ? undefined : type as 'clock_in' | 'clock_out',
      page: 1
    };
    setFilters(newFilters);
    fetchAttendanceRecords(newFilters);
  };

  const handleStatusFilter = (status: string) => {
    const newFilters = {
      ...filters,
      status: status === 'all' ? undefined : status as 'regular' | 'auto_checkout' | 'manual_adjustment',
      page: 1
    };
    setFilters(newFilters);
    fetchAttendanceRecords(newFilters);
  };

  const handleDateFilter = (field: 'start_date' | 'end_date', value: string) => {
    const newFilters = {
      ...filters,
      [field]: value || undefined,
      page: 1
    };
    setFilters(newFilters);
    fetchAttendanceRecords(newFilters);
  };

  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    fetchAttendanceRecords(newFilters);
  };

  const handleExport = async () => {
    try {
      const response = await apiService.getAttendanceReport({
        start_date: filters.start_date || '2024-01-01',
        end_date: filters.end_date || new Date().toISOString().split('T')[0],
        format: 'csv',
        employee_id: filters.employee_id,
      });

      if (response instanceof Blob) {
        const url = window.URL.createObjectURL(response);
        const link = document.createElement('a');
        link.href = url;
        link.download = `attendance-report-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (err) {
      setError(getErrorMessage(err));
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Attendance Records</h1>
          <p className="text-muted-foreground">
            View and manage employee attendance records
          </p>
        </div>
        <Button onClick={handleExport}>
          <Download className="mr-2 h-4 w-4" />
          Export CSV
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <Select onValueChange={handleEmployeeFilter} defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="Employee" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Employees</SelectItem>
                {employees.map((employee) => (
                  <SelectItem key={employee.id} value={employee.id.toString()}>
                    {employee.full_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select onValueChange={handleTypeFilter} defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="clock_in">Clock In</SelectItem>
                <SelectItem value="clock_out">Clock Out</SelectItem>
              </SelectContent>
            </Select>

            <Select onValueChange={handleStatusFilter} defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="regular">Regular</SelectItem>
                <SelectItem value="auto_checkout">Auto Checkout</SelectItem>
                <SelectItem value="manual_adjustment">Manual Adjustment</SelectItem>
              </SelectContent>
            </Select>

            <Input
              type="date"
              placeholder="Start Date"
              onChange={(e) => handleDateFilter('start_date', e.target.value)}
            />

            <Input
              type="date"
              placeholder="End Date"
              onChange={(e) => handleDateFilter('end_date', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Real-time Updates */}
      <RealTimeAttendanceUpdates />

      {/* Attendance Records Table */}
      <Card>
        <CardHeader>
          <CardTitle>Attendance Records ({totalRecords})</CardTitle>
          <CardDescription>
            Complete list of employee check-ins and check-outs
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && <ErrorMessage message={error} className="mb-4" />}

          {isLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Device</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {attendanceRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        <div className="font-medium">{record.employee_name}</div>
                      </TableCell>
                      <TableCell>
                        {formatDateTime(record.timestamp).split(' ')[0]}
                      </TableCell>
                      <TableCell>
                        {formatTime(record.timestamp)}
                      </TableCell>
                      <TableCell>
                        <StatusBadge status={record.type} />
                      </TableCell>
                      <TableCell>
                        <StatusBadge status={record.status} />
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {record.device_id}
                      </TableCell>
                      <TableCell>
                        {record.notes || '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {attendanceRecords.length === 0 && !isLoading && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No attendance records found</p>
                </div>
              )}

              {totalPages > 1 && (
                <div className="mt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Attendance;
