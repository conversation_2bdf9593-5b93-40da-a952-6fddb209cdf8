"""
Database initialization script for the Time and Attendance System
"""

import os
import sys
from datetime import date
from dotenv import load_dotenv

# Add the parent directory to the path so we can import from backend
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models import db, User, Employee, Devi<PERSON>, Setting

# Load environment variables
load_dotenv()

def create_database():
    """Create all database tables"""
    app = create_app()

    with app.app_context():
        # Create all tables
        db.create_all()
        print("✓ Database tables created successfully")

def create_admin_user():
    """Create default admin user"""
    app = create_app()

    with app.app_context():
        # Check if admin user already exists
        admin_user = User.query.filter_by(username='admin').first()

        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                first_name='System',
                last_name='Administrator',
                role='admin',
                status='active'
            )
            admin_user.set_password('admin123')  # Change this in production!

            db.session.add(admin_user)
            db.session.commit()

            print("✓ Admin user created successfully")
            print("  Username: admin")
            print("  Password: admin123")
            print("  ⚠️  Please change the default password after first login!")
        else:
            print("✓ Admin user already exists")

def create_sample_employees():
    """Create sample employees for testing"""
    app = create_app()

    with app.app_context():
        # Check if employees already exist
        if Employee.query.count() > 0:
            print("✓ Sample employees already exist")
            return

        sample_employees = [
            {
                'rfid_tag': 'ABCD1234',
                'first_name': 'John',
                'last_name': 'Doe',
                'email': '<EMAIL>',
                'phone': '+1234567890',
                'department': 'Engineering',
                'position': 'Software Developer',
                'hourly_rate': 25.00,
                'leave_balance': 25.0,
                'sick_leave_balance': 10.0,
                'employment_start_date': date(2023, 1, 15)
            },
            {
                'rfid_tag': 'EFGH5678',
                'first_name': 'Jane',
                'last_name': 'Smith',
                'email': '<EMAIL>',
                'phone': '+1234567891',
                'department': 'Marketing',
                'position': 'Marketing Manager',
                'hourly_rate': 30.00,
                'leave_balance': 25.0,
                'sick_leave_balance': 10.0,
                'employment_start_date': date(2023, 2, 1)
            },
            {
                'rfid_tag': 'IJKL9012',
                'first_name': 'Bob',
                'last_name': 'Johnson',
                'email': '<EMAIL>',
                'phone': '+1234567892',
                'department': 'Engineering',
                'position': 'Senior Developer',
                'hourly_rate': 35.00,
                'leave_balance': 25.0,
                'sick_leave_balance': 10.0,
                'employment_start_date': date(2022, 6, 1)
            },
            {
                'rfid_tag': 'MNOP3456',
                'first_name': 'Alice',
                'last_name': 'Brown',
                'email': '<EMAIL>',
                'phone': '+1234567893',
                'department': 'HR',
                'position': 'HR Specialist',
                'hourly_rate': 22.00,
                'leave_balance': 25.0,
                'sick_leave_balance': 10.0,
                'employment_start_date': date(2023, 3, 15)
            }
        ]

        for emp_data in sample_employees:
            employee = Employee(**emp_data)
            db.session.add(employee)

        db.session.commit()
        print(f"✓ Created {len(sample_employees)} sample employees")

def create_sample_device():
    """Create a sample device for testing"""
    app = create_app()

    with app.app_context():
        # Check if device already exists
        if Device.query.count() > 0:
            print("✓ Sample device already exists")
            return

        device = Device(
            name='Main Entrance Scanner',
            location='Building A - Main Entrance',
            mac_address='AA:BB:CC:DD:EE:FF',
            ip_address='*************',
            firmware_version='1.0.0',
            status='offline'
        )

        db.session.add(device)
        db.session.commit()

        print("✓ Sample device created successfully")
        print(f"  Device API Key: {device.api_key}")

def initialize_default_settings():
    """Initialize default system settings"""
    app = create_app()

    with app.app_context():
        # Check if settings already exist
        if Setting.query.count() > 0:
            print("✓ Default settings already exist")
            return

        default_settings = [
            {
                'setting_key': 'auto_checkout_time',
                'setting_value': '19:30',
                'description': 'Automatic checkout time (24-hour format)'
            },
            {
                'setting_key': 'work_hours_per_day',
                'setting_value': '8',
                'description': 'Standard work hours per day'
            },
            {
                'setting_key': 'work_days_per_week',
                'setting_value': '5',
                'description': 'Standard work days per week'
            },
            {
                'setting_key': 'annual_leave_days',
                'setting_value': '25',
                'description': 'Annual leave days per year'
            },
            {
                'setting_key': 'sick_leave_days',
                'setting_value': '10',
                'description': 'Sick leave days per year'
            },
            {
                'setting_key': 'overtime_multiplier',
                'setting_value': '1.5',
                'description': 'Overtime pay multiplier'
            },
            {
                'setting_key': 'company_name',
                'setting_value': 'Your Company Name',
                'description': 'Company name for reports'
            },
            {
                'setting_key': 'timezone',
                'setting_value': 'UTC',
                'description': 'System timezone'
            },
            {
                'setting_key': 'currency',
                'setting_value': 'EUR',
                'description': 'Currency for wage calculations'
            },
            {
                'setting_key': 'email_notifications',
                'setting_value': 'true',
                'description': 'Enable email notifications'
            }
        ]

        for setting_data in default_settings:
            setting = Setting(**setting_data)
            db.session.add(setting)

        db.session.commit()
        print(f"✓ Created {len(default_settings)} default settings")

def main():
    """Main initialization function"""
    print("🚀 Initializing Time and Attendance System Database...")
    print("=" * 60)

    try:
        # Create database tables
        create_database()

        # Create admin user
        create_admin_user()

        # Create sample data
        create_sample_employees()
        create_sample_device()

        # Initialize settings
        initialize_default_settings()

        print("=" * 60)
        print("✅ Database initialization completed successfully!")
        print("\n📋 Next steps:")
        print("1. Start the Flask application: python backend/app.py")
        print("2. Login with admin credentials (admin/admin123)")
        print("3. Change the default admin password")
        print("4. Configure your ESP32 device with the API key shown above")

    except Exception as e:
        print(f"❌ Error during initialization: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
