#!/usr/bin/env python3
"""
Test script to verify database connection and SQLAlchemy setup
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User

def test_database_connection():
    """Test if database connection and SQLAlchemy setup works"""
    print("🔍 Testing database connection...")
    
    try:
        # Create Flask app
        app = create_app()
        
        with app.app_context():
            # Test database connection
            print("✓ Flask app created successfully")
            
            # Test SQLAlchemy connection
            db.create_all()
            print("✓ Database tables created/verified")
            
            # Test a simple query
            user_count = User.query.count()
            print(f"✓ Database query successful - Found {user_count} users")
            
            print("🎉 Database connection test PASSED!")
            return True
            
    except Exception as e:
        print(f"❌ Database connection test FAILED: {str(e)}")
        return False

if __name__ == '__main__':
    success = test_database_connection()
    sys.exit(0 if success else 1)
