@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .sidebar-nav {
    @apply flex flex-col space-y-1;
  }
  
  .sidebar-nav-item {
    @apply flex items-center rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors;
  }
  
  .sidebar-nav-item.active {
    @apply bg-accent text-accent-foreground;
  }
  
  .data-table {
    @apply w-full border-collapse border border-border;
  }
  
  .data-table th {
    @apply border border-border bg-muted px-4 py-2 text-left font-medium;
  }
  
  .data-table td {
    @apply border border-border px-4 py-2;
  }
  
  .status-badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .status-badge.active {
    @apply bg-green-100 text-green-800;
  }
  
  .status-badge.inactive {
    @apply bg-red-100 text-red-800;
  }
  
  .status-badge.pending {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-badge.approved {
    @apply bg-green-100 text-green-800;
  }
  
  .status-badge.rejected {
    @apply bg-red-100 text-red-800;
  }
  
  .status-badge.online {
    @apply bg-green-100 text-green-800;
  }
  
  .status-badge.offline {
    @apply bg-gray-100 text-gray-800;
  }
  
  .form-error {
    @apply text-sm text-destructive mt-1;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-4 w-4 border-b-2 border-primary;
  }
  
  .page-header {
    @apply flex items-center justify-between pb-4 border-b border-border mb-6;
  }
  
  .page-title {
    @apply text-2xl font-bold tracking-tight;
  }
  
  .card-stats {
    @apply bg-card text-card-foreground rounded-lg border p-6 shadow-sm;
  }
  
  .card-stats-title {
    @apply text-sm font-medium text-muted-foreground;
  }
  
  .card-stats-value {
    @apply text-2xl font-bold;
  }
  
  .card-stats-change {
    @apply text-xs text-muted-foreground;
  }
}
