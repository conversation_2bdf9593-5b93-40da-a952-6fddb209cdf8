# Frontend Installation Guide

## Quick Start

1. **Navigate to the frontend directory:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

4. **Open your browser:**
   Navigate to http://localhost:3000

## Default Login Credentials
- **Username:** admin
- **Password:** admin123

## Prerequisites

- Node.js 16+ and npm
- Backend API running on http://localhost:5000

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## Troubleshooting

### Common Issues

1. **Port 3000 already in use:**
   ```bash
   # Kill the process using port 3000
   npx kill-port 3000
   # Or use a different port
   npm run dev -- --port 3001
   ```

2. **API connection issues:**
   - Make sure the backend server is running on http://localhost:5000
   - Check the proxy configuration in vite.config.ts

3. **Module not found errors:**
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

4. **TypeScript errors:**
   ```bash
   # Run type checking to see detailed errors
   npm run type-check
   ```

## Environment Configuration

Create a `.env` file in the frontend directory:

```env
VITE_API_BASE_URL=http://localhost:5000
VITE_APP_NAME=Time & Attendance System
```

## Production Build

```bash
# Build for production
npm run build

# Preview the production build
npm run preview
```

The build artifacts will be stored in the `dist/` directory.

## Development Tips

1. **Hot Reload:** The development server supports hot module replacement
2. **TypeScript:** All files should use TypeScript for type safety
3. **Linting:** Run `npm run lint` before committing changes
4. **Components:** Use the existing UI components in `src/components/ui/`

## Project Structure

```
frontend/
├── src/
│   ├── components/     # Reusable components
│   ├── pages/          # Page components
│   ├── services/       # API services
│   ├── types/          # TypeScript types
│   ├── lib/            # Utility functions
│   └── contexts/       # React contexts
├── public/             # Static assets
└── dist/               # Build output (generated)
```

## Next Steps

After successful installation:
1. Explore the dashboard at http://localhost:3000
2. Try logging in with the default credentials
3. Navigate through different sections (Employees, Attendance, etc.)
4. Check the browser console for any errors
5. Review the README.md for detailed documentation
