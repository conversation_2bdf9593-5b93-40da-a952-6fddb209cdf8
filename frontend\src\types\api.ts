// API Response Types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  total?: number;
  page?: number;
  limit?: number;
  pages?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

// Authentication Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  user: User;
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'manager' | 'viewer';
  status: 'active' | 'inactive';
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  password: string;
  role?: 'admin' | 'manager' | 'viewer';
  status?: 'active' | 'inactive';
}

export interface UpdateUserRequest {
  email?: string;
  first_name?: string;
  last_name?: string;
  role?: 'admin' | 'manager' | 'viewer';
  status?: 'active' | 'inactive';
}

export interface UpdatePasswordRequest {
  current_password?: string;
  new_password: string;
}

// Employee Types
export interface Employee {
  id: number;
  rfid_tag: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email?: string;
  phone?: string;
  department?: string;
  position?: string;
  hourly_rate: number;
  leave_balance: number;
  sick_leave_balance: number;
  employment_start_date: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface CreateEmployeeRequest {
  rfid_tag: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  department?: string;
  position?: string;
  hourly_rate: number;
  leave_balance?: number;
  sick_leave_balance?: number;
  employment_start_date: string;
  status?: 'active' | 'inactive';
}

export interface UpdateEmployeeRequest {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  department?: string;
  position?: string;
  hourly_rate?: number;
  leave_balance?: number;
  sick_leave_balance?: number;
  employment_start_date?: string;
  status?: 'active' | 'inactive';
}

// Attendance Types
export interface AttendanceRecord {
  id: number;
  employee_id: number;
  employee_name: string;
  timestamp: string;
  type: 'clock_in' | 'clock_out';
  device_id: string;
  status: 'regular' | 'auto_checkout' | 'manual_adjustment';
  notes?: string;
  created_at: string;
}

export interface CreateAttendanceRequest {
  employee_rfid: string;
  timestamp: string;
  type: 'clock_in' | 'clock_out';
  device_id: string;
  status?: 'regular' | 'auto_checkout' | 'manual_adjustment';
  notes?: string;
}

// Leave Types
export interface LeaveRequest {
  id: number;
  employee_id: number;
  employee_name: string;
  start_date: string;
  end_date: string;
  type: 'sick_leave' | 'regular_leave';
  status: 'pending' | 'approved' | 'rejected';
  approver_id?: number;
  approver_name?: string;
  request_date: string;
  notes?: string;
}

export interface CreateLeaveRequest {
  employee_id: number;
  start_date: string;
  end_date: string;
  type: 'sick_leave' | 'regular_leave';
  notes?: string;
}

export interface UpdateLeaveStatusRequest {
  status: 'approved' | 'rejected';
  notes?: string;
}

// Device Types
export interface Device {
  id: number;
  name: string;
  location?: string;
  mac_address: string;
  ip_address?: string;
  api_key: string;
  last_sync?: string;
  status: 'online' | 'offline';
  firmware_version?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateDeviceRequest {
  name: string;
  location?: string;
  mac_address: string;
  ip_address?: string;
  firmware_version?: string;
}

export interface UpdateDeviceRequest {
  name?: string;
  location?: string;
  ip_address?: string;
  firmware_version?: string;
  status?: 'online' | 'offline';
}

// Settings Types
export interface Setting {
  key: string;
  value: string;
  description?: string;
  updated_at: string;
}

export interface UpdateSettingRequest {
  value: string;
  description?: string;
}

// Report Types
export interface ReportParams {
  employee_id?: number;
  department?: string;
  start_date: string;
  end_date: string;
  format?: 'json' | 'csv' | 'pdf';
}

export interface AttendanceReportData {
  employee_id: number;
  employee_name: string;
  department: string;
  position: string;
  hours_worked: number;
  clock_ins: number;
  clock_outs: number;
  auto_checkouts: number;
  hourly_rate: number;
  total_wages: number;
}

export interface LeaveReportData {
  employee_id: number;
  employee_name: string;
  department: string;
  leave_balance: number;
  sick_leave_balance: number;
  total_leave_taken: number;
  sick_leave_taken: number;
  regular_leave_taken: number;
  pending_requests: number;
  approved_requests: number;
  rejected_requests: number;
}

export interface PayrollReportData {
  employee_id: number;
  employee_name: string;
  department: string;
  position: string;
  hourly_rate: number;
  regular_hours: number;
  overtime_hours: number;
  total_hours: number;
  regular_pay: number;
  overtime_pay: number;
  gross_pay: number;
  tax_amount: number;
  net_pay: number;
}

export interface ReportResponse<T> {
  report_type: string;
  period: {
    start_date: string;
    end_date: string;
  };
  data: T[];
  summary: Record<string, number>;
}

// Dashboard Types
export interface DashboardStats {
  total_employees: number;
  active_employees: number;
  present_today: number;
  absent_today: number;
  pending_leave_requests: number;
  online_devices: number;
  total_devices: number;
}

// Filter and Search Types
export interface EmployeeFilters {
  status?: 'active' | 'inactive';
  department?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface AttendanceFilters {
  employee_id?: number;
  start_date?: string;
  end_date?: string;
  type?: 'clock_in' | 'clock_out';
  status?: 'regular' | 'auto_checkout' | 'manual_adjustment';
  page?: number;
  limit?: number;
}

export interface LeaveFilters {
  employee_id?: number;
  status?: 'pending' | 'approved' | 'rejected';
  start_date?: string;
  end_date?: string;
  type?: 'sick_leave' | 'regular_leave';
  page?: number;
  limit?: number;
}

export interface DeviceFilters {
  status?: 'online' | 'offline';
  page?: number;
  limit?: number;
}

export interface UserFilters {
  role?: 'admin' | 'manager' | 'viewer';
  status?: 'active' | 'inactive';
  search?: string;
  page?: number;
  limit?: number;
}
