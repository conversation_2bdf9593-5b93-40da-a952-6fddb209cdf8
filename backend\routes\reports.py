"""
Reports generation routes for the Time and Attendance System
"""

import csv
import io
from datetime import datetime, date, timedelta
from flask import Blueprint, request, jsonify, current_app, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import and_, func

from models import Employee, AttendanceRecord, LeaveRequest, db
from utils.logging import log_user_action
from utils.auth import require_role

reports_bp = Blueprint('reports', __name__)

def calculate_hours_worked(employee_id, start_date, end_date):
    """
    Calculate hours worked for an employee in a date range
    """
    records = AttendanceRecord.query.filter(
        and_(
            AttendanceRecord.employee_id == employee_id,
            AttendanceRecord.timestamp >= start_date,
            AttendanceRecord.timestamp <= end_date,
            AttendanceRecord.status != 'auto_checkout'  # Exclude auto-checkouts
        )
    ).order_by(AttendanceRecord.timestamp).all()

    total_hours = 0
    clock_in_time = None

    for record in records:
        if record.type == 'clock_in':
            clock_in_time = record.timestamp
        elif record.type == 'clock_out' and clock_in_time:
            # Calculate hours between clock in and clock out
            time_diff = record.timestamp - clock_in_time
            hours = time_diff.total_seconds() / 3600
            total_hours += hours
            clock_in_time = None

    return round(total_hours, 2)

@reports_bp.route('/attendance', methods=['GET'])
@jwt_required()
def generate_attendance_report():
    """
    Generate attendance report
    """
    try:
        # Get query parameters
        employee_id = request.args.get('employee_id')
        department = request.args.get('department')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        format_type = request.args.get('format', 'csv')

        if not start_date or not end_date:
            return jsonify({'error': 'start_date and end_date are required'}), 400

        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            end_dt = end_dt.replace(hour=23, minute=59, second=59)
        except ValueError:
            return jsonify({'error': 'Invalid date format (YYYY-MM-DD)'}), 400

        # Build employee query
        employee_query = Employee.query.filter(Employee.status == 'active')

        if employee_id:
            try:
                emp_id = int(employee_id)
                employee_query = employee_query.filter(Employee.id == emp_id)
            except ValueError:
                return jsonify({'error': 'Invalid employee_id format'}), 400

        if department:
            employee_query = employee_query.filter(Employee.department == department)

        employees = employee_query.order_by(Employee.first_name, Employee.last_name).all()

        if not employees:
            return jsonify({'error': 'No employees found'}), 404

        # Generate report data
        report_data = []

        for employee in employees:
            # Get attendance records for the period
            records = AttendanceRecord.query.filter(
                and_(
                    AttendanceRecord.employee_id == employee.id,
                    AttendanceRecord.timestamp >= start_dt,
                    AttendanceRecord.timestamp <= end_dt
                )
            ).order_by(AttendanceRecord.timestamp).all()

            # Calculate hours worked
            hours_worked = calculate_hours_worked(employee.id, start_dt, end_dt)

            # Count attendance records
            clock_ins = len([r for r in records if r.type == 'clock_in'])
            clock_outs = len([r for r in records if r.type == 'clock_out'])
            auto_checkouts = len([r for r in records if r.status == 'auto_checkout'])

            # Calculate wages
            wages = hours_worked * float(employee.hourly_rate)

            report_data.append({
                'employee_id': employee.id,
                'employee_name': employee.full_name,
                'department': employee.department or '',
                'position': employee.position or '',
                'hours_worked': hours_worked,
                'clock_ins': clock_ins,
                'clock_outs': clock_outs,
                'auto_checkouts': auto_checkouts,
                'hourly_rate': float(employee.hourly_rate),
                'total_wages': round(wages, 2)
            })

        if format_type.lower() == 'csv':
            # Generate CSV
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=[
                'employee_id', 'employee_name', 'department', 'position',
                'hours_worked', 'clock_ins', 'clock_outs', 'auto_checkouts',
                'hourly_rate', 'total_wages'
            ])

            writer.writeheader()
            writer.writerows(report_data)

            # Create file-like object
            csv_data = output.getvalue()
            output.close()

            csv_buffer = io.BytesIO()
            csv_buffer.write(csv_data.encode('utf-8'))
            csv_buffer.seek(0)

            filename = f'attendance_report_{start_date}_to_{end_date}.csv'

            # Log action
            current_user_id = get_jwt_identity()
            log_user_action(
                user_id=current_user_id,
                action='generate_report',
                entity_type='attendance_report',
                details=f'Generated attendance report for {start_date} to {end_date}'
            )

            return send_file(
                csv_buffer,
                mimetype='text/csv',
                as_attachment=True,
                download_name=filename
            )

        else:
            # Return JSON data
            return jsonify({
                'report_type': 'attendance',
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'data': report_data,
                'summary': {
                    'total_employees': len(report_data),
                    'total_hours': sum(row['hours_worked'] for row in report_data),
                    'total_wages': sum(row['total_wages'] for row in report_data)
                }
            }), 200

    except Exception as e:
        current_app.logger.error(f'Generate attendance report error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@reports_bp.route('/leave', methods=['GET'])
@jwt_required()
def generate_leave_report():
    """
    Generate leave report
    """
    try:
        # Get query parameters
        employee_id = request.args.get('employee_id')
        department = request.args.get('department')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        format_type = request.args.get('format', 'csv')

        if not start_date or not end_date:
            return jsonify({'error': 'start_date and end_date are required'}), 400

        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': 'Invalid date format (YYYY-MM-DD)'}), 400

        # Build employee query
        employee_query = Employee.query.filter(Employee.status == 'active')

        if employee_id:
            try:
                emp_id = int(employee_id)
                employee_query = employee_query.filter(Employee.id == emp_id)
            except ValueError:
                return jsonify({'error': 'Invalid employee_id format'}), 400

        if department:
            employee_query = employee_query.filter(Employee.department == department)

        employees = employee_query.order_by(Employee.first_name, Employee.last_name).all()

        if not employees:
            return jsonify({'error': 'No employees found'}), 404

        # Generate report data
        report_data = []

        for employee in employees:
            # Get leave requests for the period
            leave_requests = LeaveRequest.query.filter(
                and_(
                    LeaveRequest.employee_id == employee.id,
                    LeaveRequest.start_date >= start_dt,
                    LeaveRequest.end_date <= end_dt
                )
            ).all()

            # Calculate leave statistics
            total_leave_days = 0
            sick_leave_days = 0
            regular_leave_days = 0
            pending_requests = 0
            approved_requests = 0
            rejected_requests = 0

            for leave_req in leave_requests:
                days = (leave_req.end_date - leave_req.start_date).days + 1

                if leave_req.status == 'approved':
                    total_leave_days += days
                    if leave_req.type == 'sick_leave':
                        sick_leave_days += days
                    else:
                        regular_leave_days += days

                if leave_req.status == 'pending':
                    pending_requests += 1
                elif leave_req.status == 'approved':
                    approved_requests += 1
                elif leave_req.status == 'rejected':
                    rejected_requests += 1

            report_data.append({
                'employee_id': employee.id,
                'employee_name': employee.full_name,
                'department': employee.department or '',
                'leave_balance': float(employee.leave_balance),
                'sick_leave_balance': float(employee.sick_leave_balance),
                'total_leave_taken': total_leave_days,
                'sick_leave_taken': sick_leave_days,
                'regular_leave_taken': regular_leave_days,
                'pending_requests': pending_requests,
                'approved_requests': approved_requests,
                'rejected_requests': rejected_requests
            })

        if format_type.lower() == 'csv':
            # Generate CSV
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=[
                'employee_id', 'employee_name', 'department',
                'leave_balance', 'sick_leave_balance',
                'total_leave_taken', 'sick_leave_taken', 'regular_leave_taken',
                'pending_requests', 'approved_requests', 'rejected_requests'
            ])

            writer.writeheader()
            writer.writerows(report_data)

            # Create file-like object
            csv_data = output.getvalue()
            output.close()

            csv_buffer = io.BytesIO()
            csv_buffer.write(csv_data.encode('utf-8'))
            csv_buffer.seek(0)

            filename = f'leave_report_{start_date}_to_{end_date}.csv'

            # Log action
            current_user_id = get_jwt_identity()
            log_user_action(
                user_id=current_user_id,
                action='generate_report',
                entity_type='leave_report',
                details=f'Generated leave report for {start_date} to {end_date}'
            )

            return send_file(
                csv_buffer,
                mimetype='text/csv',
                as_attachment=True,
                download_name=filename
            )

        else:
            # Return JSON data
            return jsonify({
                'report_type': 'leave',
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'data': report_data,
                'summary': {
                    'total_employees': len(report_data),
                    'total_leave_taken': sum(row['total_leave_taken'] for row in report_data),
                    'total_sick_leave': sum(row['sick_leave_taken'] for row in report_data),
                    'total_regular_leave': sum(row['regular_leave_taken'] for row in report_data)
                }
            }), 200

    except Exception as e:
        current_app.logger.error(f'Generate leave report error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@reports_bp.route('/payroll', methods=['GET'])
@jwt_required()
@require_role(['admin', 'manager'])
def generate_payroll_report():
    """
    Generate payroll report
    """
    try:
        # Get query parameters
        employee_id = request.args.get('employee_id')
        department = request.args.get('department')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        format_type = request.args.get('format', 'csv')

        if not start_date or not end_date:
            return jsonify({'error': 'start_date and end_date are required'}), 400

        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            end_dt = end_dt.replace(hour=23, minute=59, second=59)
        except ValueError:
            return jsonify({'error': 'Invalid date format (YYYY-MM-DD)'}), 400

        # Build employee query
        employee_query = Employee.query.filter(Employee.status == 'active')

        if employee_id:
            try:
                emp_id = int(employee_id)
                employee_query = employee_query.filter(Employee.id == emp_id)
            except ValueError:
                return jsonify({'error': 'Invalid employee_id format'}), 400

        if department:
            employee_query = employee_query.filter(Employee.department == department)

        employees = employee_query.order_by(Employee.first_name, Employee.last_name).all()

        if not employees:
            return jsonify({'error': 'No employees found'}), 404

        # Generate report data
        report_data = []

        for employee in employees:
            # Calculate hours worked
            hours_worked = calculate_hours_worked(employee.id, start_dt, end_dt)

            # Calculate regular and overtime hours (assuming 40 hours per week is standard)
            period_days = (end_dt.date() - start_dt.date()).days + 1
            period_weeks = period_days / 7
            standard_hours = period_weeks * 40

            regular_hours = min(hours_worked, standard_hours)
            overtime_hours = max(0, hours_worked - standard_hours)

            # Calculate wages
            hourly_rate = float(employee.hourly_rate)
            regular_pay = regular_hours * hourly_rate
            overtime_pay = overtime_hours * hourly_rate * 1.5  # 1.5x for overtime
            gross_pay = regular_pay + overtime_pay

            # Simple tax calculation (this would be more complex in real implementation)
            tax_rate = 0.20  # 20% tax rate
            tax_amount = gross_pay * tax_rate
            net_pay = gross_pay - tax_amount

            report_data.append({
                'employee_id': employee.id,
                'employee_name': employee.full_name,
                'department': employee.department or '',
                'position': employee.position or '',
                'hourly_rate': hourly_rate,
                'regular_hours': round(regular_hours, 2),
                'overtime_hours': round(overtime_hours, 2),
                'total_hours': hours_worked,
                'regular_pay': round(regular_pay, 2),
                'overtime_pay': round(overtime_pay, 2),
                'gross_pay': round(gross_pay, 2),
                'tax_amount': round(tax_amount, 2),
                'net_pay': round(net_pay, 2)
            })

        if format_type.lower() == 'csv':
            # Generate CSV
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=[
                'employee_id', 'employee_name', 'department', 'position',
                'hourly_rate', 'regular_hours', 'overtime_hours', 'total_hours',
                'regular_pay', 'overtime_pay', 'gross_pay', 'tax_amount', 'net_pay'
            ])

            writer.writeheader()
            writer.writerows(report_data)

            # Create file-like object
            csv_data = output.getvalue()
            output.close()

            csv_buffer = io.BytesIO()
            csv_buffer.write(csv_data.encode('utf-8'))
            csv_buffer.seek(0)

            filename = f'payroll_report_{start_date}_to_{end_date}.csv'

            # Log action
            current_user_id = get_jwt_identity()
            log_user_action(
                user_id=current_user_id,
                action='generate_report',
                entity_type='payroll_report',
                details=f'Generated payroll report for {start_date} to {end_date}'
            )

            return send_file(
                csv_buffer,
                mimetype='text/csv',
                as_attachment=True,
                download_name=filename
            )

        else:
            # Return JSON data
            return jsonify({
                'report_type': 'payroll',
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'data': report_data,
                'summary': {
                    'total_employees': len(report_data),
                    'total_hours': sum(row['total_hours'] for row in report_data),
                    'total_gross_pay': sum(row['gross_pay'] for row in report_data),
                    'total_tax': sum(row['tax_amount'] for row in report_data),
                    'total_net_pay': sum(row['net_pay'] for row in report_data)
                }
            }), 200

    except Exception as e:
        current_app.logger.error(f'Generate payroll report error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500
