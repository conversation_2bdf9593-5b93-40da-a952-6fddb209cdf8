/**
 * WebSocket Connection Status Indicator
 * Shows the current WebSocket connection status in the header
 */

import React from 'react';
import { Wifi, WifiOff, AlertCircle } from 'lucide-react';
import { useWebSocket } from '@/contexts/WebSocketContext';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

const WebSocketStatus: React.FC = () => {
  const { isConnected, connectionError } = useWebSocket();

  const getStatusIcon = () => {
    if (connectionError) {
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
    
    if (isConnected) {
      return <Wifi className="w-4 h-4 text-green-500" />;
    }
    
    return <WifiOff className="w-4 h-4 text-gray-400" />;
  };

  const getStatusText = () => {
    if (connectionError) {
      return `Connection Error: ${connectionError}`;
    }
    
    if (isConnected) {
      return 'Real-time updates connected';
    }
    
    return 'Real-time updates disconnected';
  };

  const getStatusColor = () => {
    if (connectionError) {
      return 'text-red-500';
    }
    
    if (isConnected) {
      return 'text-green-500';
    }
    
    return 'text-gray-400';
  };

  const handleRetryConnection = () => {
    // The WebSocket service will automatically try to reconnect
    window.location.reload();
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={connectionError ? handleRetryConnection : undefined}
          >
            {getStatusIcon()}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-center">
            <p className={`text-sm font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </p>
            {connectionError && (
              <p className="text-xs text-gray-500 mt-1">
                Click to retry connection
              </p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default WebSocketStatus;
