/**
 * WebSocket Context Provider
 * Manages WebSocket connection and provides real-time updates to components
 */

import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import { websocketService, NotificationEvent, RealTimeEvent } from '@/services/websocket';
import { useAuth } from './AuthContext';

interface WebSocketContextType {
  isConnected: boolean;
  connectionError: string | null;
  notifications: NotificationEvent[];
  addNotification: (notification: Omit<NotificationEvent, 'id'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  joinRoom: (room: string) => void;
  leaveRoom: (room: string) => void;
  onRealTimeUpdate: (callback: (event: RealTimeEvent) => void) => () => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

interface WebSocketProviderProps {
  children: ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const { token, isAuthenticated } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [notifications, setNotifications] = useState<NotificationEvent[]>([]);

  // Connect to WebSocket when authenticated
  useEffect(() => {
    if (isAuthenticated && token) {
      connectWebSocket();
    } else {
      disconnectWebSocket();
    }

    return () => {
      disconnectWebSocket();
    };
  }, [isAuthenticated, token]);

  const connectWebSocket = useCallback(async () => {
    if (!token) return;

    try {
      setConnectionError(null);
      await websocketService.connect(token);
      setIsConnected(true);
      console.log('WebSocket connected successfully');
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      setConnectionError(error instanceof Error ? error.message : 'Connection failed');
      setIsConnected(false);
    }
  }, [token]);

  const disconnectWebSocket = useCallback(() => {
    websocketService.disconnect();
    setIsConnected(false);
    setConnectionError(null);
  }, []);

  // Setup WebSocket event listeners
  useEffect(() => {
    // Connection status listeners
    const handleConnect = () => {
      setIsConnected(true);
      setConnectionError(null);
    };

    const handleDisconnect = () => {
      setIsConnected(false);
    };

    const handleError = (error: any) => {
      setConnectionError(error.message || 'WebSocket error');
    };

    const handleMaxReconnectAttempts = () => {
      setConnectionError('Failed to reconnect after multiple attempts');
    };

    // Notification listener
    const handleNotification = (notification: NotificationEvent) => {
      addNotification(notification);
    };

    // Subscribe to events
    websocketService.on('connect', handleConnect);
    websocketService.on('disconnect', handleDisconnect);
    websocketService.on('error', handleError);
    websocketService.on('max_reconnect_attempts', handleMaxReconnectAttempts);
    websocketService.on('notification', handleNotification);

    return () => {
      // Cleanup listeners
      websocketService.off('connect', handleConnect);
      websocketService.off('disconnect', handleDisconnect);
      websocketService.off('error', handleError);
      websocketService.off('max_reconnect_attempts', handleMaxReconnectAttempts);
      websocketService.off('notification', handleNotification);
    };
  }, []);

  const addNotification = useCallback((notification: Omit<NotificationEvent, 'id'>) => {
    const newNotification: NotificationEvent = {
      ...notification,
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };

    setNotifications(prev => [newNotification, ...prev]);

    // Auto-remove notification after 5 seconds for non-error notifications
    if (notification.type !== 'error') {
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, 5000);
    }
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const joinRoom = useCallback((room: string) => {
    websocketService.joinRoom(room);
  }, []);

  const leaveRoom = useCallback((room: string) => {
    websocketService.leaveRoom(room);
  }, []);

  const onRealTimeUpdate = useCallback((callback: (event: RealTimeEvent) => void) => {
    websocketService.on('real_time_update', callback);
    
    // Return cleanup function
    return () => {
      websocketService.off('real_time_update', callback);
    };
  }, []);

  const contextValue: WebSocketContextType = {
    isConnected,
    connectionError,
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
    joinRoom,
    leaveRoom,
    onRealTimeUpdate
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocket = (): WebSocketContextType => {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

// Custom hooks for specific real-time updates
export const useAttendanceUpdates = (callback: (data: any) => void) => {
  useEffect(() => {
    websocketService.on('attendance_update', callback);
    return () => {
      websocketService.off('attendance_update', callback);
    };
  }, [callback]);
};

export const useDeviceUpdates = (callback: (data: any) => void) => {
  useEffect(() => {
    websocketService.on('device_update', callback);
    return () => {
      websocketService.off('device_update', callback);
    };
  }, [callback]);
};

export const useUserUpdates = (callback: (data: any) => void) => {
  useEffect(() => {
    websocketService.on('user_update', callback);
    return () => {
      websocketService.off('user_update', callback);
    };
  }, [callback]);
};

export const useEmployeeUpdates = (callback: (data: any) => void) => {
  useEffect(() => {
    websocketService.on('employee_update', callback);
    return () => {
      websocketService.off('employee_update', callback);
    };
  }, [callback]);
};

export const useSettingsUpdates = (callback: (data: any) => void) => {
  useEffect(() => {
    websocketService.on('settings_update', callback);
    return () => {
      websocketService.off('settings_update', callback);
    };
  }, [callback]);
};

export default WebSocketContext;
