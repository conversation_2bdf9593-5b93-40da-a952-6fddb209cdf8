"""
Leave management routes for the Time and Attendance System
"""

from datetime import datetime, date
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import and_, or_

from models import LeaveRequest, Employee, User, db
from utils.validators import validate_leave_request_data, validate_pagination_params
from utils.logging import log_user_action
from utils.auth import require_role, get_current_user

leave_bp = Blueprint('leave', __name__)

@leave_bp.route('', methods=['GET'])
@jwt_required()
def get_leave_requests():
    """
    Get all leave requests with optional filtering and pagination
    """
    try:
        # Get query parameters
        employee_id = request.args.get('employee_id')
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        leave_type = request.args.get('type')
        page = request.args.get('page', '1')
        limit = request.args.get('limit', '20')

        # Validate pagination
        page_num, limit_num = validate_pagination_params(page, limit)

        # Build query
        query = LeaveRequest.query

        # Apply filters
        if employee_id:
            try:
                emp_id = int(employee_id)
                query = query.filter(LeaveRequest.employee_id == emp_id)
            except ValueError:
                return jsonify({'error': 'Invalid employee_id format'}), 400

        if status:
            if status not in ['pending', 'approved', 'rejected']:
                return jsonify({'error': 'Invalid status'}), 400
            query = query.filter(LeaveRequest.status == status)

        if start_date:
            try:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(LeaveRequest.start_date >= start_dt)
            except ValueError:
                return jsonify({'error': 'Invalid start_date format (YYYY-MM-DD)'}), 400

        if end_date:
            try:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(LeaveRequest.end_date <= end_dt)
            except ValueError:
                return jsonify({'error': 'Invalid end_date format (YYYY-MM-DD)'}), 400

        if leave_type:
            if leave_type not in ['sick_leave', 'regular_leave']:
                return jsonify({'error': 'Invalid type'}), 400
            query = query.filter(LeaveRequest.type == leave_type)

        # Order by request date descending
        query = query.order_by(LeaveRequest.request_date.desc())

        # Paginate
        pagination = query.paginate(
            page=page_num,
            per_page=limit_num,
            error_out=False
        )

        requests = [req.to_dict() for req in pagination.items]

        return jsonify({
            'data': requests,
            'total': pagination.total,
            'page': page_num,
            'limit': limit_num,
            'pages': pagination.pages
        }), 200

    except Exception as e:
        current_app.logger.error(f'Get leave requests error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@leave_bp.route('', methods=['POST'])
@jwt_required()
@require_role(['admin', 'manager'])
def create_leave_request():
    """
    Create new leave request
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate data
        validation_error = validate_leave_request_data(data)
        if validation_error:
            return jsonify({'error': validation_error}), 400

        # Check if employee exists
        employee = Employee.query.get(data['employee_id'])
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404

        if employee.status != 'active':
            return jsonify({'error': 'Employee is inactive'}), 400

        # Parse dates
        start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()

        # Check for overlapping leave requests
        overlapping_request = LeaveRequest.query.filter(
            and_(
                LeaveRequest.employee_id == data['employee_id'],
                LeaveRequest.status.in_(['pending', 'approved']),
                or_(
                    and_(LeaveRequest.start_date <= start_date, LeaveRequest.end_date >= start_date),
                    and_(LeaveRequest.start_date <= end_date, LeaveRequest.end_date >= end_date),
                    and_(LeaveRequest.start_date >= start_date, LeaveRequest.end_date <= end_date)
                )
            )
        ).first()

        if overlapping_request:
            return jsonify({'error': 'Overlapping leave request exists'}), 409

        # Calculate leave days
        leave_days = (end_date - start_date).days + 1

        # Check leave balance
        if data['type'] == 'regular_leave':
            if employee.leave_balance < leave_days:
                return jsonify({'error': 'Insufficient leave balance'}), 400
        elif data['type'] == 'sick_leave':
            if employee.sick_leave_balance < leave_days:
                return jsonify({'error': 'Insufficient sick leave balance'}), 400

        # Create leave request
        leave_request = LeaveRequest(
            employee_id=data['employee_id'],
            start_date=start_date,
            end_date=end_date,
            type=data['type'],
            notes=data.get('notes')
        )

        db.session.add(leave_request)
        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='create',
            entity_type='leave_request',
            entity_id=leave_request.id,
            details=f'Created leave request for employee {employee.full_name}'
        )

        return jsonify(leave_request.to_dict()), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Create leave request error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@leave_bp.route('/<int:request_id>', methods=['PUT'])
@jwt_required()
@require_role(['admin', 'manager'])
def update_leave_request(request_id):
    """
    Update leave request
    """
    try:
        leave_request = LeaveRequest.query.get(request_id)
        if not leave_request:
            return jsonify({'error': 'Leave request not found'}), 404

        # Only allow updates to pending requests
        if leave_request.status != 'pending':
            return jsonify({'error': 'Can only update pending requests'}), 400

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Update fields
        if 'start_date' in data:
            try:
                start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
                if start_date < date.today():
                    return jsonify({'error': 'Start date cannot be in the past'}), 400
                leave_request.start_date = start_date
            except ValueError:
                return jsonify({'error': 'Invalid start_date format (YYYY-MM-DD)'}), 400

        if 'end_date' in data:
            try:
                end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
                if end_date < leave_request.start_date:
                    return jsonify({'error': 'End date must be after start date'}), 400
                leave_request.end_date = end_date
            except ValueError:
                return jsonify({'error': 'Invalid end_date format (YYYY-MM-DD)'}), 400

        if 'type' in data:
            if data['type'] not in ['sick_leave', 'regular_leave']:
                return jsonify({'error': 'Invalid type'}), 400
            leave_request.type = data['type']

        if 'notes' in data:
            leave_request.notes = data['notes']

        # Validate updated request doesn't overlap with others
        overlapping_request = LeaveRequest.query.filter(
            and_(
                LeaveRequest.id != request_id,
                LeaveRequest.employee_id == leave_request.employee_id,
                LeaveRequest.status.in_(['pending', 'approved']),
                or_(
                    and_(LeaveRequest.start_date <= leave_request.start_date, LeaveRequest.end_date >= leave_request.start_date),
                    and_(LeaveRequest.start_date <= leave_request.end_date, LeaveRequest.end_date >= leave_request.end_date),
                    and_(LeaveRequest.start_date >= leave_request.start_date, LeaveRequest.end_date <= leave_request.end_date)
                )
            )
        ).first()

        if overlapping_request:
            return jsonify({'error': 'Updated request would overlap with existing request'}), 409

        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='update',
            entity_type='leave_request',
            entity_id=leave_request.id,
            details=f'Updated leave request for employee {leave_request.employee.full_name}'
        )

        return jsonify(leave_request.to_dict()), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Update leave request error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@leave_bp.route('/<int:request_id>/status', methods=['PUT'])
@jwt_required()
@require_role(['admin', 'manager'])
def update_leave_status(request_id):
    """
    Update leave request status (approve/reject)
    """
    try:
        leave_request = LeaveRequest.query.get(request_id)
        if not leave_request:
            return jsonify({'error': 'Leave request not found'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        status = data.get('status')
        if not status:
            return jsonify({'error': 'Status is required'}), 400

        if status not in ['approved', 'rejected']:
            return jsonify({'error': 'Status must be approved or rejected'}), 400

        # Only allow status changes from pending
        if leave_request.status != 'pending':
            return jsonify({'error': 'Can only change status of pending requests'}), 400

        current_user = get_current_user()

        # Update status
        leave_request.status = status
        leave_request.approver_id = current_user.id

        if 'notes' in data:
            leave_request.notes = data['notes']

        # If approved, deduct from leave balance
        if status == 'approved':
            leave_days = (leave_request.end_date - leave_request.start_date).days + 1
            employee = leave_request.employee

            if leave_request.type == 'regular_leave':
                if employee.leave_balance < leave_days:
                    return jsonify({'error': 'Insufficient leave balance'}), 400
                employee.leave_balance -= leave_days
            elif leave_request.type == 'sick_leave':
                if employee.sick_leave_balance < leave_days:
                    return jsonify({'error': 'Insufficient sick leave balance'}), 400
                employee.sick_leave_balance -= leave_days

        db.session.commit()

        # Log action
        log_user_action(
            user_id=current_user.id,
            action=f'leave_{status}',
            entity_type='leave_request',
            entity_id=leave_request.id,
            details=f'{status.title()} leave request for employee {leave_request.employee.full_name}'
        )

        return jsonify(leave_request.to_dict()), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Update leave status error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@leave_bp.route('/<int:request_id>', methods=['DELETE'])
@jwt_required()
@require_role(['admin'])
def delete_leave_request(request_id):
    """
    Delete leave request
    """
    try:
        leave_request = LeaveRequest.query.get(request_id)
        if not leave_request:
            return jsonify({'error': 'Leave request not found'}), 404

        # If approved request is being deleted, restore leave balance
        if leave_request.status == 'approved':
            leave_days = (leave_request.end_date - leave_request.start_date).days + 1
            employee = leave_request.employee

            if leave_request.type == 'regular_leave':
                employee.leave_balance += leave_days
            elif leave_request.type == 'sick_leave':
                employee.sick_leave_balance += leave_days

        employee_name = leave_request.employee.full_name

        db.session.delete(leave_request)
        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='delete',
            entity_type='leave_request',
            entity_id=request_id,
            details=f'Deleted leave request for employee {employee_name}'
        )

        return jsonify({'message': 'Leave request deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Delete leave request error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500
