import React, { useEffect, useState } from 'react';
import { Plus, Check, X, Eye } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorMessage from '@/components/common/ErrorMessage';
import StatusBadge from '@/components/common/StatusBadge';
import Pagination from '@/components/common/Pagination';
import { apiService } from '@/services/api';
import { LeaveRequest, LeaveFilters, Employee } from '@/types/api';
import { formatDate, getErrorMessage } from '@/lib/utils';
import { usePermissions } from '@/contexts/AuthContext';

const Leave: React.FC = () => {
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [selectedRequest, setSelectedRequest] = useState<LeaveRequest | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRequests, setTotalRequests] = useState(0);
  const [filters, setFilters] = useState<LeaveFilters>({
    page: 1,
    limit: 20,
  });

  const permissions = usePermissions();

  const fetchLeaveRequests = async (newFilters?: LeaveFilters) => {
    try {
      setIsLoading(true);
      setError(null);

      const filtersToUse = newFilters || filters;
      const response = await apiService.getLeaveRequests(filtersToUse);

      setLeaveRequests(response.data);
      setTotalRequests(response.total);
      setTotalPages(response.pages);
      setCurrentPage(response.page);
    } catch (err) {
      setError(getErrorMessage(err));
    } finally {
      setIsLoading(false);
    }
  };

  const fetchEmployees = async () => {
    try {
      const response = await apiService.getEmployees({ limit: 1000 });
      setEmployees(response.data);
    } catch (err) {
      console.error('Failed to fetch employees:', err);
    }
  };

  useEffect(() => {
    fetchLeaveRequests();
    fetchEmployees();
  }, []);

  const handleEmployeeFilter = (employeeId: string) => {
    const newFilters = {
      ...filters,
      employee_id: employeeId === 'all' ? undefined : parseInt(employeeId),
      page: 1
    };
    setFilters(newFilters);
    fetchLeaveRequests(newFilters);
  };

  const handleStatusFilter = (status: string) => {
    const newFilters = {
      ...filters,
      status: status === 'all' ? undefined : status as 'pending' | 'approved' | 'rejected',
      page: 1
    };
    setFilters(newFilters);
    fetchLeaveRequests(newFilters);
  };

  const handleTypeFilter = (type: string) => {
    const newFilters = {
      ...filters,
      type: type === 'all' ? undefined : type as 'sick_leave' | 'regular_leave',
      page: 1
    };
    setFilters(newFilters);
    fetchLeaveRequests(newFilters);
  };

  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    fetchLeaveRequests(newFilters);
  };

  const handleViewRequest = (request: LeaveRequest) => {
    setSelectedRequest(request);
    setIsDialogOpen(true);
  };

  const handleApproveRequest = async (request: LeaveRequest) => {
    if (!permissions.canApproveLeave()) return;

    try {
      await apiService.updateLeaveStatus(request.id, { status: 'approved' });
      fetchLeaveRequests();
    } catch (err) {
      setError(getErrorMessage(err));
    }
  };

  const handleRejectRequest = async (request: LeaveRequest) => {
    if (!permissions.canApproveLeave()) return;

    try {
      await apiService.updateLeaveStatus(request.id, { status: 'rejected' });
      fetchLeaveRequests();
    } catch (err) {
      setError(getErrorMessage(err));
    }
  };

  const calculateLeaveDays = (startDate: string, endDate: string): number => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays + 1; // Include both start and end dates
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Leave Requests</h1>
          <p className="text-muted-foreground">
            Manage employee leave requests and approvals
          </p>
        </div>
        {permissions.canManageEmployees() && (
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Request
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Select onValueChange={handleEmployeeFilter} defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="Employee" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Employees</SelectItem>
                {employees.map((employee) => (
                  <SelectItem key={employee.id} value={employee.id.toString()}>
                    {employee.full_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select onValueChange={handleStatusFilter} defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>

            <Select onValueChange={handleTypeFilter} defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="regular_leave">Regular Leave</SelectItem>
                <SelectItem value="sick_leave">Sick Leave</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Leave Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle>Leave Requests ({totalRequests})</CardTitle>
          <CardDescription>
            All leave requests in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && <ErrorMessage message={error} className="mb-4" />}

          {isLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>End Date</TableHead>
                    <TableHead>Days</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Request Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {leaveRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div className="font-medium">{request.employee_name}</div>
                      </TableCell>
                      <TableCell>
                        <StatusBadge status={request.type} />
                      </TableCell>
                      <TableCell>{formatDate(request.start_date)}</TableCell>
                      <TableCell>{formatDate(request.end_date)}</TableCell>
                      <TableCell>
                        {calculateLeaveDays(request.start_date, request.end_date)} days
                      </TableCell>
                      <TableCell>
                        <StatusBadge status={request.status} />
                      </TableCell>
                      <TableCell>{formatDate(request.request_date)}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleViewRequest(request)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {permissions.canApproveLeave() && request.status === 'pending' && (
                            <>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleApproveRequest(request)}
                                className="text-green-600 hover:text-green-700"
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleRejectRequest(request)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {leaveRequests.length === 0 && !isLoading && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No leave requests found</p>
                </div>
              )}

              {totalPages > 1 && (
                <div className="mt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Leave Request Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Leave Request Details</DialogTitle>
            <DialogDescription>
              Detailed information about the selected leave request
            </DialogDescription>
          </DialogHeader>
          {selectedRequest && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Employee</label>
                  <p className="text-sm text-muted-foreground">{selectedRequest.employee_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Type</label>
                  <StatusBadge status={selectedRequest.type} />
                </div>
                <div>
                  <label className="text-sm font-medium">Start Date</label>
                  <p className="text-sm text-muted-foreground">{formatDate(selectedRequest.start_date)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">End Date</label>
                  <p className="text-sm text-muted-foreground">{formatDate(selectedRequest.end_date)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Total Days</label>
                  <p className="text-sm text-muted-foreground">
                    {calculateLeaveDays(selectedRequest.start_date, selectedRequest.end_date)} days
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Status</label>
                  <StatusBadge status={selectedRequest.status} />
                </div>
                <div>
                  <label className="text-sm font-medium">Request Date</label>
                  <p className="text-sm text-muted-foreground">{formatDate(selectedRequest.request_date)}</p>
                </div>
                {selectedRequest.approver_name && (
                  <div>
                    <label className="text-sm font-medium">Approved By</label>
                    <p className="text-sm text-muted-foreground">{selectedRequest.approver_name}</p>
                  </div>
                )}
              </div>
              {selectedRequest.notes && (
                <div>
                  <label className="text-sm font-medium">Notes</label>
                  <p className="text-sm text-muted-foreground">{selectedRequest.notes}</p>
                </div>
              )}
              {permissions.canApproveLeave() && selectedRequest.status === 'pending' && (
                <div className="flex space-x-2 pt-4">
                  <Button
                    onClick={() => {
                      handleApproveRequest(selectedRequest);
                      setIsDialogOpen(false);
                    }}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Check className="mr-2 h-4 w-4" />
                    Approve
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => {
                      handleRejectRequest(selectedRequest);
                      setIsDialogOpen(false);
                    }}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Reject
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Leave;
