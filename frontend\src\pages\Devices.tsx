import React, { useEffect, useState } from 'react';
import { Plus, Monitor, Wifi, WifiOff, MoreHorizontal, Key, Trash2, Edit } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorMessage from '@/components/common/ErrorMessage';
import Pagination from '@/components/common/Pagination';
import { apiService } from '@/services/api';
import { Device, DeviceFilters } from '@/types/api';
import { formatDate, getErrorMessage } from '@/lib/utils';
import { usePermissions } from '@/contexts/AuthContext';
import CreateDeviceDialog from '@/components/devices/CreateDeviceDialog';
import EditDeviceDialog from '@/components/devices/EditDeviceDialog';
import DeleteDeviceDialog from '@/components/devices/DeleteDeviceDialog';
import ApiKeyDialog from '@/components/devices/ApiKeyDialog';

const Devices: React.FC = () => {
  const [devices, setDevices] = useState<Device[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalDevices, setTotalDevices] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [showApiKeyDialog, setShowApiKeyDialog] = useState(false);
  const [newApiKey, setNewApiKey] = useState<string>('');

  const permissions = usePermissions();
  const limit = 10;

  const fetchDevices = async (page: number = 1) => {
    try {
      setIsLoading(true);
      setError(null);

      const filters: DeviceFilters = {
        page,
        limit,
      };

      if (statusFilter) {
        filters.status = statusFilter as 'online' | 'offline';
      }

      const response = await apiService.getDevices(filters);
      setDevices(response.data);
      setTotalPages(response.pages);
      setTotalDevices(response.total);
      setCurrentPage(page);
    } catch (err) {
      setError(getErrorMessage(err));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDevices();
  }, [statusFilter]);

  const handleSearch = () => {
    fetchDevices(1);
  };

  const handlePageChange = (page: number) => {
    fetchDevices(page);
  };

  const handleRegenerateApiKey = async (device: Device) => {
    try {
      const response = await apiService.regenerateDeviceKey(device.id);
      setNewApiKey(response.api_key);
      setSelectedDevice(device);
      setShowApiKeyDialog(true);
      fetchDevices(currentPage);
    } catch (err) {
      setError(getErrorMessage(err));
    }
  };

  const handleDeleteDevice = async () => {
    if (!selectedDevice) return;

    try {
      await apiService.deleteDevice(selectedDevice.id);
      setShowDeleteDialog(false);
      setSelectedDevice(null);
      fetchDevices(currentPage);
    } catch (err) {
      setError(getErrorMessage(err));
    }
  };

  const getStatusBadge = (status: string) => {
    return (
      <Badge variant={status === 'online' ? 'default' : 'secondary'}>
        {status === 'online' ? (
          <Wifi className="w-3 h-3 mr-1" />
        ) : (
          <WifiOff className="w-3 h-3 mr-1" />
        )}
        {status}
      </Badge>
    );
  };

  const filteredDevices = devices.filter(device =>
    device.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    device.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    device.mac_address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading && devices.length === 0) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Device Management</h1>
          <p className="text-muted-foreground">
            Manage ESP32 devices and monitor their status
          </p>
        </div>
        {permissions.canManageDevices() && (
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Device
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Devices</CardTitle>
            <Monitor className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDevices}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Online Devices</CardTitle>
            <Wifi className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {devices.filter(d => d.status === 'online').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Offline Devices</CardTitle>
            <WifiOff className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {devices.filter(d => d.status === 'offline').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Devices</CardTitle>
          <CardDescription>
            Monitor and manage your ESP32 attendance devices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search devices..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Status</SelectItem>
                <SelectItem value="online">Online</SelectItem>
                <SelectItem value="offline">Offline</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearch}>Search</Button>
          </div>

          {error && <ErrorMessage message={error} />}

          {/* Devices Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>MAC Address</TableHead>
                  <TableHead>IP Address</TableHead>
                  <TableHead>Firmware</TableHead>
                  <TableHead>Last Sync</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDevices.map((device) => (
                  <TableRow key={device.id}>
                    <TableCell className="font-medium">{device.name}</TableCell>
                    <TableCell>{device.location || '-'}</TableCell>
                    <TableCell>{getStatusBadge(device.status)}</TableCell>
                    <TableCell className="font-mono text-sm">{device.mac_address}</TableCell>
                    <TableCell>{device.ip_address || '-'}</TableCell>
                    <TableCell>{device.firmware_version || '-'}</TableCell>
                    <TableCell>
                      {device.last_sync ? formatDate(device.last_sync) : 'Never'}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {permissions.canManageDevices() && (
                            <>
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedDevice(device);
                                  setShowEditDialog(true);
                                }}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleRegenerateApiKey(device)}
                              >
                                <Key className="mr-2 h-4 w-4" />
                                Regenerate API Key
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedDevice(device);
                                  setShowDeleteDialog(true);
                                }}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredDevices.length === 0 && !isLoading && (
            <div className="text-center py-8 text-muted-foreground">
              No devices found
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-6">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <CreateDeviceDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={() => {
          setShowCreateDialog(false);
          fetchDevices(currentPage);
        }}
      />

      <EditDeviceDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        device={selectedDevice}
        onSuccess={() => {
          setShowEditDialog(false);
          setSelectedDevice(null);
          fetchDevices(currentPage);
        }}
      />

      <DeleteDeviceDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        device={selectedDevice}
        onConfirm={handleDeleteDevice}
      />

      <ApiKeyDialog
        open={showApiKeyDialog}
        onOpenChange={setShowApiKeyDialog}
        device={selectedDevice}
        apiKey={newApiKey}
      />
    </div>
  );
};

export default Devices;
