"""
Attendance management routes for the Time and Attendance System
"""

from datetime import datetime, time
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import and_, or_

from models import AttendanceRecord, Employee, Devi<PERSON>, db
from utils.validators import validate_attendance_data, validate_pagination_params
from utils.logging import log_user_action, log_device_action
from utils.auth import require_role, require_api_key

attendance_bp = Blueprint('attendance', __name__)

@attendance_bp.route('', methods=['GET'])
@jwt_required()
def get_attendance():
    """
    Get all attendance records with optional filtering and pagination
    """
    try:
        # Get query parameters
        employee_id = request.args.get('employee_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        record_type = request.args.get('type')
        status = request.args.get('status')
        page = request.args.get('page', '1')
        limit = request.args.get('limit', '50')

        # Validate pagination
        page_num, limit_num = validate_pagination_params(page, limit)

        # Build query
        query = AttendanceRecord.query

        # Apply filters
        if employee_id:
            try:
                emp_id = int(employee_id)
                query = query.filter(AttendanceRecord.employee_id == emp_id)
            except ValueError:
                return jsonify({'error': 'Invalid employee_id format'}), 400

        if start_date:
            try:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(AttendanceRecord.timestamp >= start_dt)
            except ValueError:
                return jsonify({'error': 'Invalid start_date format (YYYY-MM-DD)'}), 400

        if end_date:
            try:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')
                end_dt = end_dt.replace(hour=23, minute=59, second=59)
                query = query.filter(AttendanceRecord.timestamp <= end_dt)
            except ValueError:
                return jsonify({'error': 'Invalid end_date format (YYYY-MM-DD)'}), 400

        if record_type:
            if record_type not in ['clock_in', 'clock_out']:
                return jsonify({'error': 'Invalid type. Must be clock_in or clock_out'}), 400
            query = query.filter(AttendanceRecord.type == record_type)

        if status:
            if status not in ['regular', 'auto_checkout', 'manual_adjustment']:
                return jsonify({'error': 'Invalid status'}), 400
            query = query.filter(AttendanceRecord.status == status)

        # Order by timestamp descending
        query = query.order_by(AttendanceRecord.timestamp.desc())

        # Paginate
        pagination = query.paginate(
            page=page_num,
            per_page=limit_num,
            error_out=False
        )

        records = [record.to_dict() for record in pagination.items]

        return jsonify({
            'data': records,
            'total': pagination.total,
            'page': page_num,
            'limit': limit_num,
            'pages': pagination.pages
        }), 200

    except Exception as e:
        current_app.logger.error(f'Get attendance error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@attendance_bp.route('', methods=['POST'])
@require_api_key
def create_attendance():
    """
    Create new attendance record (used by ESP32 devices)
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate data
        validation_error = validate_attendance_data(data)
        if validation_error:
            return jsonify({'error': validation_error}), 400

        # Find employee by RFID tag
        employee = Employee.query.filter_by(rfid_tag=data['employee_rfid']).first()
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404

        if employee.status != 'active':
            return jsonify({'error': 'Employee is inactive'}), 400

        # Parse timestamp
        timestamp = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))

        # Create attendance record
        record = AttendanceRecord(
            employee_id=employee.id,
            timestamp=timestamp,
            type=data['type'],
            device_id=data['device_id'],
            status=data.get('status', 'regular'),
            notes=data.get('notes')
        )

        db.session.add(record)
        db.session.commit()

        # Update device last sync
        device = request.current_device
        device.last_sync = datetime.utcnow()
        device.status = 'online'
        db.session.commit()

        # Log device action
        log_device_action(
            device_id=device.id,
            action='attendance_record',
            details=f'Created attendance record for employee {employee.full_name}'
        )

        return jsonify(record.to_dict()), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Create attendance error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@attendance_bp.route('/batch', methods=['POST'])
@require_api_key
def create_attendance_batch():
    """
    Create multiple attendance records (used by ESP32 devices for syncing)
    """
    try:
        data = request.get_json()
        if not data or 'records' not in data:
            return jsonify({'error': 'No records provided'}), 400

        records = data['records']
        if not isinstance(records, list):
            return jsonify({'error': 'Records must be an array'}), 400

        success_count = 0
        failed_count = 0
        failed_records = []

        for index, record_data in enumerate(records):
            try:
                # Validate record data
                validation_error = validate_attendance_data(record_data)
                if validation_error:
                    failed_count += 1
                    failed_records.append({
                        'index': index,
                        'reason': validation_error
                    })
                    continue

                # Find employee by RFID tag
                employee = Employee.query.filter_by(rfid_tag=record_data['employee_rfid']).first()
                if not employee:
                    failed_count += 1
                    failed_records.append({
                        'index': index,
                        'reason': 'Employee not found'
                    })
                    continue

                if employee.status != 'active':
                    failed_count += 1
                    failed_records.append({
                        'index': index,
                        'reason': 'Employee is inactive'
                    })
                    continue

                # Parse timestamp
                timestamp = datetime.fromisoformat(record_data['timestamp'].replace('Z', '+00:00'))

                # Check if record already exists (prevent duplicates)
                existing_record = AttendanceRecord.query.filter(
                    and_(
                        AttendanceRecord.employee_id == employee.id,
                        AttendanceRecord.timestamp == timestamp,
                        AttendanceRecord.type == record_data['type']
                    )
                ).first()

                if existing_record:
                    failed_count += 1
                    failed_records.append({
                        'index': index,
                        'reason': 'Record already exists'
                    })
                    continue

                # Create attendance record
                record = AttendanceRecord(
                    employee_id=employee.id,
                    timestamp=timestamp,
                    type=record_data['type'],
                    device_id=record_data['device_id'],
                    status=record_data.get('status', 'regular'),
                    notes=record_data.get('notes')
                )

                db.session.add(record)
                success_count += 1

            except Exception as e:
                failed_count += 1
                failed_records.append({
                    'index': index,
                    'reason': f'Processing error: {str(e)}'
                })

        # Commit all successful records
        if success_count > 0:
            db.session.commit()

            # Update device last sync
            device = request.current_device
            device.last_sync = datetime.utcnow()
            device.status = 'online'
            db.session.commit()

            # Log device action
            log_device_action(
                device_id=device.id,
                action='batch_sync',
                details=f'Synced {success_count} attendance records'
            )

        return jsonify({
            'success_count': success_count,
            'failed_count': failed_count,
            'failed_records': failed_records
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Batch attendance error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@attendance_bp.route('/<int:record_id>', methods=['PUT'])
@jwt_required()
@require_role(['admin'])
def update_attendance(record_id):
    """
    Update attendance record (admin only)
    """
    try:
        record = AttendanceRecord.query.get(record_id)
        if not record:
            return jsonify({'error': 'Attendance record not found'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Update fields
        if 'timestamp' in data:
            try:
                record.timestamp = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
            except ValueError:
                return jsonify({'error': 'Invalid timestamp format'}), 400

        if 'type' in data:
            if data['type'] not in ['clock_in', 'clock_out']:
                return jsonify({'error': 'Invalid type'}), 400
            record.type = data['type']

        if 'status' in data:
            if data['status'] not in ['regular', 'auto_checkout', 'manual_adjustment']:
                return jsonify({'error': 'Invalid status'}), 400
            record.status = data['status']

        if 'notes' in data:
            record.notes = data['notes']

        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='update',
            entity_type='attendance',
            entity_id=record.id,
            details=f'Updated attendance record for employee {record.employee.full_name}'
        )

        return jsonify(record.to_dict()), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Update attendance error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@attendance_bp.route('/<int:record_id>', methods=['DELETE'])
@jwt_required()
@require_role(['admin'])
def delete_attendance(record_id):
    """
    Delete attendance record (admin only)
    """
    try:
        record = AttendanceRecord.query.get(record_id)
        if not record:
            return jsonify({'error': 'Attendance record not found'}), 404

        employee_name = record.employee.full_name

        db.session.delete(record)
        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='delete',
            entity_type='attendance',
            entity_id=record_id,
            details=f'Deleted attendance record for employee {employee_name}'
        )

        return jsonify({'message': 'Attendance record deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Delete attendance error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500
