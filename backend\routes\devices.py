"""
Device management routes for the Time and Attendance System
"""

from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import or_

from models import Device, db
from utils.validators import validate_device_data, validate_pagination_params
from utils.logging import log_user_action
from utils.auth import require_role

devices_bp = Blueprint('devices', __name__)

@devices_bp.route('', methods=['GET'])
@jwt_required()
def get_devices():
    """
    Get all devices with optional filtering and pagination
    """
    try:
        # Get query parameters
        status = request.args.get('status')
        page = request.args.get('page', '1')
        limit = request.args.get('limit', '20')

        # Validate pagination
        page_num, limit_num = validate_pagination_params(page, limit)

        # Build query
        query = Device.query

        # Apply filters
        if status:
            if status not in ['online', 'offline']:
                return jsonify({'error': 'Invalid status. Must be online or offline'}), 400
            query = query.filter(Device.status == status)

        # Order by name
        query = query.order_by(Device.name)

        # Paginate
        pagination = query.paginate(
            page=page_num,
            per_page=limit_num,
            error_out=False
        )

        devices = [device.to_dict() for device in pagination.items]

        return jsonify({
            'data': devices,
            'total': pagination.total,
            'page': page_num,
            'limit': limit_num,
            'pages': pagination.pages
        }), 200

    except Exception as e:
        current_app.logger.error(f'Get devices error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@devices_bp.route('/<int:device_id>', methods=['GET'])
@jwt_required()
def get_device(device_id):
    """
    Get device by ID
    """
    try:
        device = Device.query.get(device_id)

        if not device:
            return jsonify({'error': 'Device not found'}), 404

        return jsonify(device.to_dict()), 200

    except Exception as e:
        current_app.logger.error(f'Get device error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@devices_bp.route('', methods=['POST'])
@jwt_required()
@require_role(['admin'])
def create_device():
    """
    Register new device
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate data
        validation_error = validate_device_data(data)
        if validation_error:
            return jsonify({'error': validation_error}), 400

        # Check if MAC address already exists
        existing_device = Device.query.filter_by(mac_address=data['mac_address']).first()
        if existing_device:
            return jsonify({'error': 'MAC address already exists'}), 409

        # Create device
        device = Device(
            name=data['name'],
            location=data.get('location'),
            mac_address=data['mac_address'],
            ip_address=data.get('ip_address'),
            firmware_version=data.get('firmware_version')
        )

        db.session.add(device)
        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='create',
            entity_type='device',
            entity_id=device.id,
            details=f'Registered device: {device.name}'
        )

        return jsonify(device.to_dict()), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Create device error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@devices_bp.route('/<int:device_id>', methods=['PUT'])
@jwt_required()
@require_role(['admin'])
def update_device(device_id):
    """
    Update device
    """
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': 'Device not found'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Update fields
        if 'name' in data:
            if not data['name']:
                return jsonify({'error': 'Name is required'}), 400
            device.name = data['name']

        if 'location' in data:
            device.location = data['location']

        if 'ip_address' in data:
            device.ip_address = data['ip_address']

        if 'firmware_version' in data:
            device.firmware_version = data['firmware_version']

        if 'status' in data:
            if data['status'] not in ['online', 'offline']:
                return jsonify({'error': 'Invalid status'}), 400
            device.status = data['status']

        device.updated_at = datetime.utcnow()
        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='update',
            entity_type='device',
            entity_id=device.id,
            details=f'Updated device: {device.name}'
        )

        return jsonify(device.to_dict()), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Update device error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@devices_bp.route('/<int:device_id>', methods=['DELETE'])
@jwt_required()
@require_role(['admin'])
def delete_device(device_id):
    """
    Delete device
    """
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': 'Device not found'}), 404

        device_name = device.name

        db.session.delete(device)
        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='delete',
            entity_type='device',
            entity_id=device_id,
            details=f'Deleted device: {device_name}'
        )

        return jsonify({'message': 'Device deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Delete device error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@devices_bp.route('/<int:device_id>/regenerate-key', methods=['POST'])
@jwt_required()
@require_role(['admin'])
def regenerate_api_key(device_id):
    """
    Regenerate API key for device
    """
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': 'Device not found'}), 404

        # Generate new API key
        new_api_key = device.regenerate_api_key()
        device.updated_at = datetime.utcnow()

        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='regenerate_api_key',
            entity_type='device',
            entity_id=device.id,
            details=f'Regenerated API key for device: {device.name}'
        )

        return jsonify({
            'id': device.id,
            'api_key': new_api_key
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Regenerate API key error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@devices_bp.route('/sync-status', methods=['POST'])
@jwt_required()
@require_role(['admin'])
def update_device_sync_status():
    """
    Update sync status for all devices based on last sync time
    """
    try:
        # Get devices that haven't synced in the last 5 minutes
        cutoff_time = datetime.utcnow() - timedelta(minutes=5)

        # Set devices to offline if they haven't synced recently
        offline_devices = Device.query.filter(
            or_(
                Device.last_sync < cutoff_time,
                Device.last_sync.is_(None)
            )
        ).all()

        for device in offline_devices:
            device.status = 'offline'

        # Set devices to online if they have synced recently
        online_devices = Device.query.filter(
            Device.last_sync >= cutoff_time
        ).all()

        for device in online_devices:
            device.status = 'online'

        db.session.commit()

        return jsonify({
            'message': 'Device sync status updated',
            'offline_count': len(offline_devices),
            'online_count': len(online_devices)
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Update device sync status error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500
