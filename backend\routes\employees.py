"""
Employee management routes for the Time and Attendance System
"""

from datetime import datetime, date
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import and_, or_

from models import Employee, User, AttendanceRecord, LeaveRequest, db
from utils.validators import validate_employee_data, validate_pagination_params
from utils.logging import log_user_action
from utils.auth import require_role

employees_bp = Blueprint('employees', __name__)

@employees_bp.route('', methods=['GET'])
@jwt_required()
def get_employees():
    """
    Get all employees with optional filtering and pagination
    """
    try:
        # Get query parameters
        status = request.args.get('status')
        department = request.args.get('department')
        search = request.args.get('search')
        page = request.args.get('page', '1')
        limit = request.args.get('limit', '20')

        # Validate pagination
        page_num, limit_num = validate_pagination_params(page, limit)

        # Build query
        query = Employee.query

        # Apply filters
        if status:
            query = query.filter(Employee.status == status)

        if department:
            query = query.filter(Employee.department == department)

        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Employee.first_name.ilike(search_term),
                    Employee.last_name.ilike(search_term),
                    Employee.email.ilike(search_term),
                    Employee.rfid_tag.ilike(search_term)
                )
            )

        # Order by name
        query = query.order_by(Employee.first_name, Employee.last_name)

        # Paginate
        pagination = query.paginate(
            page=page_num,
            per_page=limit_num,
            error_out=False
        )

        employees = [employee.to_dict() for employee in pagination.items]

        return jsonify({
            'data': employees,
            'total': pagination.total,
            'page': page_num,
            'limit': limit_num,
            'pages': pagination.pages
        }), 200

    except Exception as e:
        current_app.logger.error(f'Get employees error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@employees_bp.route('/<int:employee_id>', methods=['GET'])
@jwt_required()
def get_employee(employee_id):
    """
    Get employee by ID
    """
    try:
        employee = Employee.query.get(employee_id)

        if not employee:
            return jsonify({'error': 'Employee not found'}), 404

        return jsonify(employee.to_dict()), 200

    except Exception as e:
        current_app.logger.error(f'Get employee error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@employees_bp.route('', methods=['POST'])
@jwt_required()
@require_role(['admin', 'manager'])
def create_employee():
    """
    Create new employee
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate data
        validation_error = validate_employee_data(data)
        if validation_error:
            return jsonify({'error': validation_error}), 400

        # Check if RFID tag already exists
        existing_employee = Employee.query.filter_by(rfid_tag=data['rfid_tag']).first()
        if existing_employee:
            return jsonify({'error': 'RFID tag already exists'}), 409

        # Check if email already exists (if provided)
        if data.get('email'):
            existing_email = Employee.query.filter_by(email=data['email']).first()
            if existing_email:
                return jsonify({'error': 'Email already exists'}), 409

        # Parse employment start date
        employment_start_date = datetime.strptime(data['employment_start_date'], '%Y-%m-%d').date()

        # Create employee
        employee = Employee(
            rfid_tag=data['rfid_tag'],
            first_name=data['first_name'],
            last_name=data['last_name'],
            email=data.get('email'),
            phone=data.get('phone'),
            department=data.get('department'),
            position=data.get('position'),
            hourly_rate=float(data['hourly_rate']),
            leave_balance=float(data.get('leave_balance', 0)),
            sick_leave_balance=float(data.get('sick_leave_balance', 0)),
            employment_start_date=employment_start_date,
            status=data.get('status', 'active')
        )

        db.session.add(employee)
        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='create',
            entity_type='employee',
            entity_id=employee.id,
            details=f'Created employee: {employee.full_name}'
        )

        return jsonify(employee.to_dict()), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Create employee error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@employees_bp.route('/<int:employee_id>', methods=['PUT'])
@jwt_required()
@require_role(['admin', 'manager'])
def update_employee(employee_id):
    """
    Update employee
    """
    try:
        employee = Employee.query.get(employee_id)
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate data
        validation_error = validate_employee_data(data, is_update=True)
        if validation_error:
            return jsonify({'error': validation_error}), 400

        # Check if email already exists (if changed)
        if 'email' in data and data['email'] and data['email'] != employee.email:
            existing_email = Employee.query.filter_by(email=data['email']).first()
            if existing_email:
                return jsonify({'error': 'Email already exists'}), 409

        # Update fields
        if 'first_name' in data:
            employee.first_name = data['first_name']
        if 'last_name' in data:
            employee.last_name = data['last_name']
        if 'email' in data:
            employee.email = data['email']
        if 'phone' in data:
            employee.phone = data['phone']
        if 'department' in data:
            employee.department = data['department']
        if 'position' in data:
            employee.position = data['position']
        if 'hourly_rate' in data:
            employee.hourly_rate = float(data['hourly_rate'])
        if 'leave_balance' in data:
            employee.leave_balance = float(data['leave_balance'])
        if 'sick_leave_balance' in data:
            employee.sick_leave_balance = float(data['sick_leave_balance'])
        if 'employment_start_date' in data:
            employee.employment_start_date = datetime.strptime(data['employment_start_date'], '%Y-%m-%d').date()
        if 'status' in data:
            employee.status = data['status']

        employee.updated_at = datetime.utcnow()
        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='update',
            entity_type='employee',
            entity_id=employee.id,
            details=f'Updated employee: {employee.full_name}'
        )

        return jsonify(employee.to_dict()), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Update employee error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@employees_bp.route('/<int:employee_id>', methods=['DELETE'])
@jwt_required()
@require_role(['admin'])
def delete_employee(employee_id):
    """
    Delete employee (set to inactive)
    """
    try:
        employee = Employee.query.get(employee_id)
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404

        # Set to inactive instead of deleting
        employee.status = 'inactive'
        employee.updated_at = datetime.utcnow()
        db.session.commit()

        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='delete',
            entity_type='employee',
            entity_id=employee.id,
            details=f'Deactivated employee: {employee.full_name}'
        )

        return jsonify({'message': 'Employee deactivated successfully'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Delete employee error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@employees_bp.route('/<int:employee_id>/attendance', methods=['GET'])
@jwt_required()
def get_employee_attendance(employee_id):
    """
    Get attendance records for an employee
    """
    try:
        employee = Employee.query.get(employee_id)
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404

        # Get query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        page = request.args.get('page', '1')
        limit = request.args.get('limit', '50')

        # Validate pagination
        page_num, limit_num = validate_pagination_params(page, limit)

        # Build query
        query = AttendanceRecord.query.filter_by(employee_id=employee_id)

        # Apply date filters
        if start_date:
            try:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(AttendanceRecord.timestamp >= start_dt)
            except ValueError:
                return jsonify({'error': 'Invalid start_date format (YYYY-MM-DD)'}), 400

        if end_date:
            try:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')
                # Add one day to include the entire end date
                end_dt = end_dt.replace(hour=23, minute=59, second=59)
                query = query.filter(AttendanceRecord.timestamp <= end_dt)
            except ValueError:
                return jsonify({'error': 'Invalid end_date format (YYYY-MM-DD)'}), 400

        # Order by timestamp descending
        query = query.order_by(AttendanceRecord.timestamp.desc())

        # Paginate
        pagination = query.paginate(
            page=page_num,
            per_page=limit_num,
            error_out=False
        )

        records = [record.to_dict() for record in pagination.items]

        return jsonify({
            'data': records,
            'total': pagination.total,
            'page': page_num,
            'limit': limit_num,
            'pages': pagination.pages
        }), 200

    except Exception as e:
        current_app.logger.error(f'Get employee attendance error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@employees_bp.route('/<int:employee_id>/leave', methods=['GET'])
@jwt_required()
def get_employee_leave(employee_id):
    """
    Get leave requests for an employee
    """
    try:
        employee = Employee.query.get(employee_id)
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404

        # Get query parameters
        status = request.args.get('status')
        page = request.args.get('page', '1')
        limit = request.args.get('limit', '20')

        # Validate pagination
        page_num, limit_num = validate_pagination_params(page, limit)

        # Build query
        query = LeaveRequest.query.filter_by(employee_id=employee_id)

        # Apply status filter
        if status:
            query = query.filter(LeaveRequest.status == status)

        # Order by request date descending
        query = query.order_by(LeaveRequest.request_date.desc())

        # Paginate
        pagination = query.paginate(
            page=page_num,
            per_page=limit_num,
            error_out=False
        )

        requests = [req.to_dict() for req in pagination.items]

        return jsonify({
            'data': requests,
            'total': pagination.total,
            'page': page_num,
            'limit': limit_num,
            'pages': pagination.pages
        }), 200

    except Exception as e:
        current_app.logger.error(f'Get employee leave error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500
