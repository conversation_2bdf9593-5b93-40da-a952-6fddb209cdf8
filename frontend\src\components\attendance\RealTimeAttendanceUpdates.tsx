/**
 * Real-time Attendance Updates Component
 * Shows live attendance events as they happen
 */

import React, { useState, useEffect } from 'react';
import { Clock, User, MapPin, Wifi } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatDateTime } from '@/lib/utils';
import { useAttendanceUpdates } from '@/contexts/WebSocketContext';
import { AttendanceUpdateEvent } from '@/services/websocket';

interface AttendanceEvent {
  id: string;
  action: string;
  record?: any;
  employee?: {
    id: number;
    full_name: string;
    employee_id?: string;
  };
  device?: {
    id: number;
    name: string;
    location: string;
  };
  timestamp: string;
  count?: number;
}

const RealTimeAttendanceUpdates: React.FC = () => {
  const [recentEvents, setRecentEvents] = useState<AttendanceEvent[]>([]);
  const [isVisible, setIsVisible] = useState(true);

  // Handle real-time attendance updates
  useAttendanceUpdates((data: AttendanceUpdateEvent) => {
    const event: AttendanceEvent = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      action: data.action,
      record: data.record,
      employee: data.employee,
      device: data.device,
      timestamp: new Date().toISOString(),
      count: data.count
    };

    setRecentEvents(prev => [event, ...prev.slice(0, 9)]); // Keep last 10 events

    // Auto-hide after showing for a moment
    setIsVisible(true);
    setTimeout(() => setIsVisible(false), 5000);
  });

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'created':
        return <Clock className="w-4 h-4 text-green-500" />;
      case 'updated':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'deleted':
        return <Clock className="w-4 h-4 text-red-500" />;
      case 'batch_created':
        return <Wifi className="w-4 h-4 text-purple-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getActionText = (event: AttendanceEvent) => {
    switch (event.action) {
      case 'created':
        return `${event.employee?.full_name} ${event.record?.type === 'clock_in' ? 'clocked in' : 'clocked out'}`;
      case 'updated':
        return `${event.employee?.full_name}'s attendance record was updated`;
      case 'deleted':
        return `${event.employee?.full_name}'s attendance record was deleted`;
      case 'batch_created':
        return `${event.count} attendance records synced from ${event.device?.name}`;
      default:
        return 'Attendance event occurred';
    }
  };

  const getActionBadge = (action: string) => {
    switch (action) {
      case 'created':
        return <Badge variant="default" className="bg-green-100 text-green-800">New</Badge>;
      case 'updated':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Updated</Badge>;
      case 'deleted':
        return <Badge variant="destructive">Deleted</Badge>;
      case 'batch_created':
        return <Badge variant="default" className="bg-purple-100 text-purple-800">Sync</Badge>;
      default:
        return <Badge variant="secondary">Event</Badge>;
    }
  };

  if (recentEvents.length === 0) {
    return null;
  }

  return (
    <Card className={`transition-all duration-300 ${isVisible ? 'opacity-100' : 'opacity-75'}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Wifi className="w-5 h-5 text-green-500" />
          Live Attendance Updates
        </CardTitle>
        <CardDescription>
          Real-time attendance events from all devices
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {recentEvents.map((event) => (
            <div
              key={event.id}
              className="flex items-start space-x-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
            >
              <div className="flex-shrink-0 mt-0.5">
                {getActionIcon(event.action)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-foreground">
                    {getActionText(event)}
                  </p>
                  {getActionBadge(event.action)}
                </div>
                
                <div className="mt-1 flex items-center space-x-4 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {formatDateTime(event.timestamp)}
                  </span>
                  
                  {event.device && (
                    <span className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      {event.device.location}
                    </span>
                  )}
                  
                  {event.employee?.employee_id && (
                    <span className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      ID: {event.employee.employee_id}
                    </span>
                  )}
                </div>
                
                {event.record && event.action === 'created' && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      event.record.type === 'clock_in' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {event.record.type === 'clock_in' ? 'Clock In' : 'Clock Out'}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {recentEvents.length >= 10 && (
          <div className="mt-3 text-center">
            <p className="text-xs text-muted-foreground">
              Showing last 10 events
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RealTimeAttendanceUpdates;
